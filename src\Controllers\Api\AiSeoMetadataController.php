<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use B<PERSON>zley\Core\Session;
use Brenzley\Services\ApiProviders\ApiService;

/**
 * Controller for AI SEO metadata generation
 */
class AiSeoMetadataController extends AiContentController
{
    /**
     * Generate SEO metadata
     */
    public function generate(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['title']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Title is required'], 400);
            return;
        }

        if (!isset($data['content']) || empty($data['content'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Content is required'], 400);
            return;
        }

        if (!isset($data['content_id']) || empty($data['content_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Content ID is required'], 400);
            return;
        }

        if (!isset($data['content_type']) || empty($data['content_type'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Content type is required'], 400);
            return;
        }

        $title = $data['title'];
        $content = $data['content'];
        $contentId = (int)$data['content_id'];
        $contentType = $data['content_type'];
        $parameters = $data['parameters'] ?? [];
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;

        // Validate content type
        $validContentTypes = ['post', 'ai_blog_post'];
        if (!in_array($contentType, $validContentTypes)) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid content type'], 400);
            return;
        }

        // Check if the content exists and belongs to the current user
        if ($contentType === 'post') {
            $postModel = new \Brenzley\Models\PostModel();
            $contentObj = $postModel->findById($contentId);
        } else {
            $contentObj = $this->postModel->findById($contentId);
        }

        if (!$contentObj) {
            $this->jsonResponse(['success' => false, 'message' => 'Content not found'], 404);
            return;
        }

        if ($contentObj['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to content'], 403);
            return;
        }

        // Create API service
        $apiService = new ApiService();

        // Generate SEO metadata
        $result = $apiService->generateSeoMetadata($title, $content, $parameters);

        if (!$result['success']) {
            $this->jsonResponse($result);
            return;
        }

        // Get active provider info
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        // Save the generated metadata to the database
        $metadataData = [
            'user_id' => Session::getUserId(),
            'content_id' => $contentId,
            'content_type' => $contentType,
            'meta_title' => $result['seo_metadata']['meta_title'] ?? $title,
            'meta_description' => $result['seo_metadata']['meta_description'] ?? '',
            'focus_keywords' => $result['seo_metadata']['focus_keywords'] ?? [],
            'slug' => $result['seo_metadata']['slug'] ?? $this->generateSlug($title),
            'provider' => $activeProvider['provider'] ?? 'unknown',
            'model' => $activeProvider['model'] ?? 'unknown',
            'parameters' => $parameters
        ];

        $metadataId = $this->seoModel->create($metadataData);

        if (!$metadataId) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to save SEO metadata',
                'seo_metadata' => $result['seo_metadata']
            ], 500);
            return;
        }

        // Usage is now logged by the ApiService

        $this->jsonResponse([
            'success' => true,
            'seo_metadata' => $result['seo_metadata'],
            'metadata_id' => $metadataId
        ]);
    }

    /**
     * Get SEO metadata
     */
    public function get(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get metadata ID from query parameters
        $metadataId = isset($_GET['id']) ? (int)$_GET['id'] : null;

        if (!$metadataId) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata ID is required'], 400);
            return;
        }

        // Get metadata
        $metadata = $this->seoModel->findById($metadataId);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata not found'], 404);
            return;
        }

        // Check if the metadata belongs to the current user
        if ($metadata['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to metadata'], 403);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Get SEO metadata for content
     */
    public function getForContent(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get content ID and type from query parameters
        $contentId = isset($_GET['content_id']) ? (int)$_GET['content_id'] : null;
        $contentType = $_GET['content_type'] ?? '';

        if (!$contentId) {
            $this->jsonResponse(['success' => false, 'message' => 'Content ID is required'], 400);
            return;
        }

        if (!$contentType) {
            $this->jsonResponse(['success' => false, 'message' => 'Content type is required'], 400);
            return;
        }

        // Validate content type
        $validContentTypes = ['post', 'ai_blog_post'];
        if (!in_array($contentType, $validContentTypes)) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid content type'], 400);
            return;
        }

        // Check if the content exists and belongs to the current user
        if ($contentType === 'post') {
            $postModel = new \Brenzley\Models\PostModel();
            $contentObj = $postModel->findById($contentId);
        } else {
            $contentObj = $this->postModel->findById($contentId);
        }

        if (!$contentObj) {
            $this->jsonResponse(['success' => false, 'message' => 'Content not found'], 404);
            return;
        }

        if ($contentObj['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to content'], 403);
            return;
        }

        // Get metadata
        $metadata = $this->seoModel->findByContent($contentId, $contentType);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'No metadata found for this content'], 404);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Apply SEO metadata to a post
     */
    public function applyToPost(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['metadata_id']) || empty($data['metadata_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata ID is required'], 400);
            return;
        }

        if (!isset($data['post_id']) || empty($data['post_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }

        $metadataId = (int)$data['metadata_id'];
        $postId = (int)$data['post_id'];

        // Get metadata
        $metadata = $this->seoModel->findById($metadataId);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata not found'], 404);
            return;
        }

        // Check if the metadata belongs to the current user
        if ($metadata['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to metadata'], 403);
            return;
        }

        // Check if the post exists and belongs to the current user
        $postModel = new \Brenzley\Models\PostModel();
        $post = $postModel->findById($postId);

        if (!$post) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found'], 404);
            return;
        }

        if ($post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to post'], 403);
            return;
        }

        // Apply metadata to post
        $success = $this->seoModel->applyToPost($metadataId, $postId);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to apply metadata to post'], 500);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'message' => 'Metadata applied to post successfully'
        ]);
    }

    /**
     * Generate a slug from a title
     *
     * @param string $title The title to convert to a slug
     * @return string The generated slug
     */
    private function generateSlug(string $title): string
    {
        // Convert to lowercase
        $slug = strtolower($title);

        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);

        // Remove leading and trailing hyphens
        $slug = trim($slug, '-');

        return $slug;
    }
}

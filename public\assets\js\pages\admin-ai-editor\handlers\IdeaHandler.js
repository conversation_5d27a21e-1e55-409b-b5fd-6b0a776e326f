import { getPresetFromGoal, getGoalDescription, removeMarkdown, showNotification } from '../utils/EditorUtils.js';

/**
 * Handles the "Idea" step functionalities.
 */
export class IdeaHandler {
    constructor(uiManager, stateManager, chatFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        this.elements = uiManager.elements;
    }

    init() {
        if (this.elements.generateIdeasBtn) {
            this.elements.generateIdeasBtn.addEventListener('click', () => this.generateIdeas());
        }
    }

    generateIdeas() {
        // --- Development Shortcut ---
        // This code block is for testing purposes to bypass idea/outline generation
        // and jump directly to the full content generation.
        console.log("DEV SHORTCUT: Bypassing idea generation, jumping to content.");
        this.chatFeature.addAssistantMessage("DEV SHORTCUT: Skipping to content generation for testing.");

        // 1. Set a dummy title and outline in the state
        const dummyTitle = "Comprehensive Markdown and Image Layout Test";
        const dummyOutline = [{
            heading: "Showcase Section",
            description: "A section to test all components.",
            key_points: ["Markdown works", "Images render"],
            subsections: []
        }];
        
        this.stateManager.updateState('title', dummyTitle);
        this.stateManager.setOutline(dummyOutline);
        this.stateManager.setSelectedIdea({ title: dummyTitle, preset: 'professional' });

        // 2. Switch the UI to the 'content' step
        this.stateManager.setCurrentStep('content');
        document.dispatchEvent(new CustomEvent('aieditor:stepChange', { detail: { step: 'content' } }));
        
        // --- End Development Shortcut ---
    }

    streamIdeas(topic, audience, goal) {
        this.elements.ideasContainer.innerHTML = ''; // Clear for streaming
        const ideasGrid = document.createElement('div');
        ideasGrid.className = 'ai-ideas-container'; // Re-use existing class for styling
        this.elements.ideasContainer.appendChild(ideasGrid);

        const statusMessage = document.createElement('div');
        statusMessage.className = 'ai-status-message';
        statusMessage.textContent = 'Generating ideas...';
        this.elements.ideasContainer.appendChild(statusMessage);

        let collectedContent = '';
        let currentIdeasData = []; // To store parsed idea objects
        let currentIdeaTextSnippets = []; // To store raw text for each idea card during streaming

        const eventSource = new EventSource(`/api/admin/ai/blog-ideas/stream?topic=${encodeURIComponent(topic)}&audience=${encodeURIComponent(audience)}&goal=${encodeURIComponent(goal)}`);

        eventSource.addEventListener('message', (event) => {
            try {
                const data = JSON.parse(event.data);
                switch (data.type) {
                    case 'chunk':
                        collectedContent += data.content;
                        // Attempt to parse and render incrementally
                        this.updateStreamedIdeaCards(collectedContent, ideasGrid, statusMessage, currentIdeaTextSnippets);
                        break;
                    case 'end':
                        // The 'end' event now contains a list of all successfully saved ideas
                        this.finalizeStreamedIdeas(data.content.ideas, ideasGrid, statusMessage);
                        eventSource.close();
                        this.resetGenerateButton();
                        break;
                    case 'error':
                        showNotification(data.message || 'Failed to generate ideas via stream', 'error');
                        this.elements.ideasContainer.innerHTML = `<div class="ai-error">Error: ${data.message || 'Stream error'}</div>`;
                        this.chatFeature.addAssistantMessage(`Stream error: ${data.message || 'Unknown error'}`);
                        eventSource.close();
                        this.resetGenerateButton();
                        break;
                }
            } catch (error) {
                console.error('Error handling streaming message:', error, event.data);
            }
        });

        eventSource.addEventListener('error', (error) => {
            console.error('Streaming connection error:', error);
            showNotification('Connection error during idea generation.', 'error');
            this.elements.ideasContainer.innerHTML = '<div class="ai-error">Error: Failed to connect for streaming ideas.</div>';
            this.chatFeature.addAssistantMessage('Sorry, a connection error occurred while streaming ideas.');
            eventSource.close();
            this.resetGenerateButton();
        });
    }

    updateStreamedIdeaCards(fullContent, ideasGrid, statusMessage, currentIdeaTextSnippets) {
        const ideaMarkers = fullContent.match(/IDEA\s*#\s*\d+/gi);
        if (!ideaMarkers) { // Still receiving the first idea or preamble
            if (currentIdeaTextSnippets.length === 0) currentIdeaTextSnippets.push('');
            currentIdeaTextSnippets[0] += fullContent.substring(currentIdeaTextSnippets[0].length); // Append new part of chunk
            this.createOrUpdateIdeaCardDOM(0, currentIdeaTextSnippets[0], ideasGrid);
            statusMessage.textContent = `Generating idea #1...`;
            return;
        }

        const ideaContents = fullContent.split(/IDEA\s*#\s*\d+/i).slice(1); // Content parts after each marker

        ideaMarkers.forEach((marker, index) => {
            const ideaNumberMatch = marker.match(/IDEA\s*#\s*(\d+)/i);
            const ideaIndex = ideaNumberMatch ? parseInt(ideaNumberMatch[1]) - 1 : index;

            let currentText = marker + (ideaContents[index] || '');
            currentIdeaTextSnippets[ideaIndex] = currentText;
            this.createOrUpdateIdeaCardDOM(ideaIndex, currentText, ideasGrid);
            statusMessage.textContent = `Generating idea #${ideaIndex + 1}...`;
        });
    }

    finalizeStreamedIdeas(savedIdeas, ideasGrid, statusMessage) {
        statusMessage.remove();

        if (!savedIdeas || savedIdeas.length === 0) {
            console.error("finalizeStreamedIdeas: No saved ideas received from the server.");
            this.elements.ideasContainer.innerHTML = '<div class="ai-error">Could not save generated ideas. Please try again.</div>';
            this.chatFeature.addAssistantMessage('I generated some ideas, but there was a problem saving them.');
            return;
        }

        // The ideas are already rendered. Now we just need to update them with the final
        // data (especially the db_id) and attach the final event listeners.
        savedIdeas.forEach(idea => {
            const card = ideasGrid.querySelector(`.ai-idea-card[data-id="${idea.client_id}"]`);
            if (card) {
                card.classList.remove('generating');
                // The idea object from the server now has the definitive data.
                // We re-create the object for the event listener to ensure it's clean.
                const finalIdeaData = {
                    client_id: idea.client_id,
                    db_id: idea.db_id, // The correct, unique DB ID for this idea
                    title: idea.title,
                    description: idea.description,
                    preset: getPresetFromGoal(this.elements.ideaGoalSelect.value),
                    target_audience: idea.target_audience,
                    key_points: idea.key_points
                };

                // Replace the card with a fresh one to easily manage event listeners
                const newCard = card.cloneNode(true);
                card.parentNode.replaceChild(newCard, card);
                newCard.addEventListener('click', () => this.selectIdea(finalIdeaData));
            }
        });

        this.chatFeature.addAssistantMessage(`I've generated and saved ${savedIdeas.length} article idea(s). Click one to proceed.`);
        this.addContinueButtonIfNeeded();
    }


    createOrUpdateIdeaCardDOM(index, content, ideasGrid) {
        let cardContainer = ideasGrid.querySelector(`.ai-idea-card-container[data-index="${index}"]`);
        const parsedIdea = this.parseIdeaContent(content);

        if (!cardContainer) {
            cardContainer = document.createElement('div');
            cardContainer.className = 'ai-idea-card-container';
            cardContainer.dataset.index = index;

            const card = document.createElement('div');
            card.className = 'ai-idea-card generating'; // Add 'generating' class
            card.dataset.id = `idea-${index + 1}`; // Temporary ID

            card.innerHTML = `
                <div class="ai-idea-card-number">${index + 1}</div>
                <div class="ai-idea-title">${parsedIdea.title || `Generating idea #${index + 1}...`}</div>
                <div class="ai-idea-description">${parsedIdea.description || ''}</div>
                <div class="ai-idea-target-audience">${parsedIdea.target_audience ? `<strong>Target Audience:</strong> ${parsedIdea.target_audience}` : ''}</div>
                <div class="ai-idea-key-points"></div>
                <div class="ai-idea-meta">
                    <div class="ai-idea-preset">${getPresetFromGoal(this.elements.ideaGoalSelect.value)}</div>
                </div>
            `;
            cardContainer.appendChild(card);
            ideasGrid.appendChild(cardContainer);
        }

        const card = cardContainer.querySelector('.ai-idea-card');
        // Update content
        card.querySelector('.ai-idea-title').textContent = parsedIdea.title || `Generating idea #${index + 1}...`;
        card.querySelector('.ai-idea-description').textContent = parsedIdea.description || '';
        const audienceEl = card.querySelector('.ai-idea-target-audience');
        if (parsedIdea.target_audience) {
            audienceEl.innerHTML = `<strong>Target Audience:</strong> ${parsedIdea.target_audience}`;
        } else {
            audienceEl.innerHTML = '';
        }

        const keyPointsEl = card.querySelector('.ai-idea-key-points');
        if (parsedIdea.key_points && parsedIdea.key_points.length > 0) {
            keyPointsEl.innerHTML = '<ul>' + parsedIdea.key_points.map(p => `<li>${p}</li>`).join('') + '</ul>';
        } else {
            keyPointsEl.innerHTML = '';
        }
        return card;
    }


    fetchIdeas(topic, audience, goal) {
        fetch('/api/admin/ai/blog-ideas/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({ topic, parameters: { audience, goal } })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.idea) {
                const ideas = this.formatApiIdeas(data.idea);
                this.renderIdeas(ideas);
                this.chatFeature.addAssistantMessage(`I've generated ${ideas.length} article ideas. Select one to continue.`);
            } else {
                showNotification(data.message || 'Failed to generate ideas', 'error');
                this.elements.ideasContainer.innerHTML = `<div class="ai-error">Error: ${data.message || 'API error'}</div>`;
                this.chatFeature.addAssistantMessage(`Error generating ideas: ${data.message || 'Unknown API error'}`);
            }
        })
        .catch(error => {
            console.error('Error fetching ideas:', error);
            showNotification('Failed to connect to server for idea generation.', 'error');
            this.elements.ideasContainer.innerHTML = '<div class="ai-error">Error: Server connection failed.</div>';
            this.chatFeature.addAssistantMessage('Sorry, I couldn\'t connect to the server to generate ideas.');
        })
        .finally(() => this.resetGenerateButton());
    }

    resetGenerateButton() {
        this.elements.generateIdeasBtn.disabled = false;
        this.elements.generateIdeasBtn.textContent = 'Generate Ideas';
    }

    formatApiIdeas(apiIdeaData) {
        // This function should transform the API response (which might be a single complex object or an array)
        // into a consistent array of idea objects.
        const ideas = [];
        const goal = this.elements.ideaGoalSelect.value;

        if (apiIdeaData.ideas && Array.isArray(apiIdeaData.ideas)) {
            apiIdeaData.ideas.forEach((idea, index) => {
                ideas.push({
                    id: idea.id || `idea-${index + 1}`,
                    title: idea.title || 'Untitled Idea',
                    description: idea.description || 'No description.',
                    preset: getPresetFromGoal(goal),
                    target_audience: idea.target_audience || apiIdeaData.target_audience || '',
                    key_points: idea.key_points || []
                });
            });
        } else if (apiIdeaData.title) { // Single idea object
            ideas.push({
                id: 'idea-1',
                title: apiIdeaData.title,
                description: apiIdeaData.description || 'No description.',
                preset: getPresetFromGoal(goal),
                target_audience: apiIdeaData.target_audience || '',
                key_points: apiIdeaData.key_points || []
            });
        }
        // Add more parsing logic if the API structure is different
        return ideas;
    }

    parseIdeaContent(content) {
        if (!content) return { title: '', description: '', target_audience: '', key_points: [] };
        const cleanContent = removeMarkdown(content);
        const idea = { title: '', description: '', target_audience: '', key_points: [] };

        let titleMatch = cleanContent.match(/Title\s*:(.*?)(?=Description|Target Audience|Key Points|IDEA\s*#|$)/is);
        if (!titleMatch) titleMatch = cleanContent.match(/IDEA\s*#\s*\d+\s*[:\-]?\s*(.*?)(?=\n|Description|Target Audience|Key Points|$)/i); // More flexible title match
        if (titleMatch && titleMatch[1]) idea.title = titleMatch[1].trim();
        else { // Fallback: first non-empty line after marker if any, or first line
            const lines = cleanContent.split('\n').map(l => l.trim()).filter(l => l && !l.match(/IDEA\s*#\s*\d+/i));
            if (lines.length > 0) idea.title = lines[0];
        }


        const descMatch = cleanContent.match(/Description\s*:(.*?)(?=Target Audience|Key Points|IDEA\s*#|$)/is);
        if (descMatch && descMatch[1]) idea.description = descMatch[1].trim();
        else if (idea.title) { // Fallback: content after title, before next section
            const titleEndIndex = cleanContent.toLowerCase().indexOf(idea.title.toLowerCase()) + idea.title.length;
            const remainingContent = cleanContent.substring(titleEndIndex);
            const nextSectionRegex = /Target Audience|Key Points|IDEA\s*#/i;
            const nextSectionMatch = remainingContent.match(nextSectionRegex);
            idea.description = (nextSectionMatch ? remainingContent.substring(0, nextSectionMatch.index) : remainingContent).trim();
        }


        const audienceMatch = cleanContent.match(/Target\s*Audience\s*:(.*?)(?=Key Points|IDEA\s*#|$)/is);
        if (audienceMatch && audienceMatch[1]) idea.target_audience = audienceMatch[1].trim();

        const keyPointsMatch = cleanContent.match(/Key\s*Points\s*:([\s\S]*?)(?=IDEA\s*#|$)/is);
        if (keyPointsMatch && keyPointsMatch[1]) {
            idea.key_points = keyPointsMatch[1].trim().split(/\n\s*[-*•]\s*|\n\s*\d+\.\s*/)
                               .map(p => p.trim()).filter(p => p);
        }
        return idea;
    }
    
    parseStreamingContentFallback(content) {
        // A simpler parser for when IDEA # markers are not consistently present or clear
        const ideas = [];
        // Try splitting by a common pattern like "Title:" or a double newline if sections are paragraph-separated
        const blocks = content.split(/\n\s*Title:/i);
        
        blocks.forEach((block, index) => {
            if (index === 0 && !content.toLowerCase().startsWith("title:")) { // Handle preamble before first "Title:"
                if (block.trim().length > 10) { // Arbitrary length to consider it substantial
                     // Try to parse as a single idea if it looks like one
                    const parsedPreamble = this.parseIdeaContent(block);
                    if(parsedPreamble.title) ideas.push(parsedPreamble);
                }
                return;
            }
            const ideaContent = (index > 0 ? "Title:" : "") + block; // Add "Title:" back if split by it
            const parsed = this.parseIdeaContent(ideaContent);
            if (parsed.title) { // Only add if a title was found
                ideas.push(parsed);
            }
        });

        if (ideas.length === 0 && content.trim()) { // If still no ideas, treat whole content as one idea
            ideas.push(this.parseIdeaContent(content));
        }
        return ideas.filter(idea => idea.title); // Ensure all ideas have a title
    }


    renderIdeas(ideas) {
        this.elements.ideasContainer.innerHTML = ''; // Clear previous
        const ideasGrid = document.createElement('div');
        ideasGrid.className = 'ai-ideas-container'; // Use the same class for styling consistency
        ideas.forEach(idea => this.renderIdeaCard(idea, ideasGrid));
        this.elements.ideasContainer.appendChild(ideasGrid);

        if (ideas.length > 0) {
            this.addContinueButtonIfNeeded();
        }
    }

    renderIdeaCard(idea, container) {
        const cardContainer = document.createElement('div');
        cardContainer.className = 'ai-idea-card-container'; // Wrapper for potential future styling/grid behavior

        const card = document.createElement('div');
        card.className = 'ai-idea-card';
        card.dataset.clientId = idea.client_id; // Use client_id for DOM selection
        card.dataset.id = idea.db_id; // Store the database ID

        let audienceHTML = '';
        if (idea.target_audience) {
            audienceHTML = `<div class="ai-idea-target-audience"><strong>Target Audience:</strong> ${idea.target_audience}</div>`;
        }

        let keyPointsHTML = '';
        if (idea.key_points && idea.key_points.length > 0) {
            keyPointsHTML = '<div class="ai-idea-key-points"><strong>Key Points:</strong><ul>' +
                            idea.key_points.map(point => `<li>${point}</li>`).join('') +
                            '</ul></div>';
        }
        
        card.innerHTML = `
            <div class="ai-idea-card-number">${idea.client_id ? idea.client_id.split('-')[1] : ''}</div>
            <div class="ai-idea-title">${idea.title}</div>
            <div class="ai-idea-description">${idea.description}</div>
            ${audienceHTML}
            ${keyPointsHTML}
            <div class="ai-idea-meta">
                <div class="ai-idea-preset">${idea.preset}</div>
            </div>
        `;
        // The event listener is now added in finalizeStreamedIdeas
        cardContainer.appendChild(card);
        container.appendChild(cardContainer);
    }

    selectIdea(ideaDataFromCard) { // ideaDataFromCard is the object passed from renderIdeaCard
        // Ensure the selected idea in state includes all necessary info, especially db_id
        const selectedIdeaState = {
            ...ideaDataFromCard, // Contains title, description, preset, client_id, db_id etc.
            // No, this is wrong. ideaDataFromCard *is* the full object.
            // The key is that ideaDataFromCard itself should have db_id.
        };
        this.stateManager.setSelectedIdea(ideaDataFromCard); // Store the whole idea object which now includes db_id

        // Update UI for selected card
        this.elements.ideasContainer.querySelectorAll('.ai-idea-card').forEach(card => {
            card.classList.remove('selected');
        });
        const selectedCard = this.elements.ideasContainer.querySelector(`[data-client-id="${ideaDataFromCard.client_id}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        this.chatFeature.addAssistantMessage(`You've selected "${ideaDataFromCard.title}". This will use the "${ideaDataFromCard.preset}" preset. Click "Continue" to create an outline.`);
        this.addContinueButtonIfNeeded();
    }

    addContinueButtonIfNeeded() {
        if (!this.elements.ideasContainer.querySelector('#ai-continue-to-outline')) {
            const continueBtn = document.createElement('button');
            continueBtn.id = 'ai-continue-to-outline';
            continueBtn.className = 'ai-btn ai-btn-primary ai-btn-full';
            continueBtn.style.marginTop = '1rem';
            continueBtn.textContent = 'Continue to Outline';
            continueBtn.addEventListener('click', () => {
                this.stateManager.setCurrentStep('outline');
                // The main orchestrator should handle the goToStep logic
                // This might involve emitting an event or calling a method on the orchestrator
                document.dispatchEvent(new CustomEvent('aieditor:stepChange', { detail: { step: 'outline' } }));
            });
            this.elements.ideasContainer.appendChild(continueBtn);
        }
    }
}
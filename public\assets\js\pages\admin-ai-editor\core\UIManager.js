import { showNotification as utilShowNotification } from '../utils/EditorUtils.js';

/**
 * Manages the UI for the AI Article Creator.
 */
export class UIManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.elements = this.cacheDomElements();
    }

    cacheDomElements() {
        return {
            // Main containers
            container: document.querySelector('.ai-editor-container'),

            // Process steps
            processSteps: document.querySelectorAll('.ai-process-step'),
            processPanels: document.querySelectorAll('.ai-process-panel'),

            // Header elements
            saveStatus: document.getElementById('ai-save-status'),
            saveDraftBtn: document.getElementById('ai-save-draft'),
            publishBtn: document.getElementById('ai-publish'),
            previewBtn: document.getElementById('ai-preview'),

            // Idea step elements
            ideaTopicInput: document.getElementById('idea-topic'),
            ideaAudienceInput: document.getElementById('idea-audience'),
            ideaGoalSelect: document.getElementById('idea-goal'),
            generateIdeasBtn: document.getElementById('ai-generate-ideas'),
            ideasContainer: document.getElementById('ai-ideas-container'),

            // Outline step elements
            selectedIdeaContainer: document.getElementById('ai-selected-idea'),
            outlineContainer: document.getElementById('ai-outline-container'),
            outlineMapContainer: document.getElementById('ai-outline-map-container'),
            outlineTabs: document.querySelectorAll('.ai-outline-tab'),
            outlineViews: document.querySelectorAll('.ai-outline-view'),
            toggleOutlineOptionsBtn: document.getElementById('ai-toggle-outline-options'),
            outlineOptionsContent: document.getElementById('ai-outline-options-content'),
            outlineDepthSelect: document.getElementById('ai-outline-depth'),
            outlineStyleSelect: document.getElementById('ai-outline-style'),
            tocStyleSelect: document.getElementById('ai-toc-style'),
            tocIncludeH2: document.getElementById('ai-toc-include-h2'),
            tocIncludeH3: document.getElementById('ai-toc-include-h3'),
            tocIncludeH4: document.getElementById('ai-toc-include-h4'),
            generateTocBtn: document.getElementById('ai-generate-toc'),
            tocPreview: document.getElementById('ai-toc-preview'),
            tocCode: document.getElementById('ai-toc-code'),
            copyTocCodeBtn: document.getElementById('ai-copy-toc-code'),
            regenerateOutlineBtn: document.getElementById('ai-regenerate-outline'),
            approveOutlineBtn: document.getElementById('ai-approve-outline'),

            // Content step elements
            titleInput: document.getElementById('post-title'),
            contentArea: document.getElementById('ai-content-area'),
            selectionToolbar: document.getElementById('ai-selection-toolbar'),
            contextMenu: document.getElementById('ai-context-menu'),
            formatButtons: document.querySelectorAll('.ai-format-btn'),
            contextMenuItems: document.querySelectorAll('.ai-context-menu-item'),
            headingDropdown: document.querySelector('.ai-heading-dropdown'),
            headingOptions: document.querySelectorAll('.ai-heading-option'),
            contextSubmenu: document.querySelector('.ai-context-submenu'),
            submenuItems: document.querySelectorAll('.ai-context-submenu .ai-context-menu-item'),
            linkDialog: document.getElementById('ai-link-dialog'),
            linkDialogClose: document.getElementById('ai-link-dialog-close'),
            linkUrl: document.getElementById('ai-link-url'),
            linkText: document.getElementById('ai-link-text'),
            linkTitle: document.getElementById('ai-link-title'),
            linkNewTab: document.getElementById('ai-link-new-tab'),
            linkNofollow: document.getElementById('ai-link-nofollow'),
            linkCancel: document.getElementById('ai-link-cancel'),
            linkInsert: document.getElementById('ai-link-insert'),
            
            // Table elements
            tableToolbar: document.getElementById('ai-table-toolbar'),
            tableCreateDialog: document.getElementById('ai-table-create-dialog'),
            tableRows: document.getElementById('ai-table-rows'),
            tableCols: document.getElementById('ai-table-cols'),
            tableHeader: document.getElementById('ai-table-header'),
            tableCancel: document.getElementById('ai-table-cancel'),
            tableCreate: document.getElementById('ai-table-create'),
            
            regenerateContentBtn: document.getElementById('ai-regenerate-content'),
            continueToSeoBtn: document.getElementById('ai-continue-seo'),

            // SEO step elements (removed - functionality unavailable)

            // Chat elements
            chatMessages: document.getElementById('ai-chat-messages'),
            chatInput: document.getElementById('ai-chat-input'),
            chatSendBtn: document.getElementById('ai-chat-send'),
            chatToggle: document.getElementById('ai-chat-close-btn'), // Changed ID
            chatFab: document.getElementById('ai-chat-fab'), // New FAB
            chatPanel: document.getElementById('ai-assistant-chat-panel'), // New Panel ID

            // Hidden form elements
            postForm: document.getElementById('post-form'),
            hiddenTitle: document.getElementById('hidden-title'),
            hiddenContent: document.getElementById('hidden-content'),
            hiddenMetaTitle: document.getElementById('hidden-meta-title'),
            hiddenMetaDescription: document.getElementById('hidden-meta-description'),
            hiddenFocusKeywords: document.getElementById('hidden-focus-keywords'),
            hiddenStatus: document.getElementById('hidden-status'),
            hiddenCategoryId: document.getElementById('hidden-category-id'),
            hiddenFeaturedImage: document.getElementById('hidden-featured-image'),

            // Featured image elements
            featuredImagePreview: document.getElementById('featured-image-preview')
        };
    }

    /**
     * Updates the UI to reflect the current step.
     * @param {string} step - The current step (idea, outline, content, seo).
     */
    updateStepUI(step) {
        this.elements.processSteps.forEach(stepEl => {
            const stepName = stepEl.dataset.step;
            const stepOrder = ['idea', 'outline', 'content', 'seo'];
            const isBeforeCurrent = stepOrder.indexOf(stepName) < stepOrder.indexOf(step);

            if (stepName === step) {
                stepEl.classList.add('active');
                stepEl.classList.remove('completed');
            } else if (isBeforeCurrent) {
                stepEl.classList.remove('active');
                stepEl.classList.add('completed');
            } else {
                stepEl.classList.remove('active', 'completed');
            }
        });

        this.elements.processPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `ai-step-${step}`);
        });
    }

    /**
     * Renders chat messages in the UI.
     */
    renderChatMessages() {
        const messages = this.stateManager.getChatMessages();
        this.elements.chatMessages.innerHTML = ''; // Clear existing messages

        messages.forEach(message => {
            const messageEl = document.createElement('div');
            messageEl.className = `ai-chat-message ai-message-${message.role}`;
            messageEl.innerHTML = `
                <div class="ai-message-avatar">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        ${message.role === 'assistant' ?
                            '<circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path>' :
                            '<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle>'}
                    </svg>
                </div>
                <div class="ai-message-content">
                    <p>${message.content}</p>
                </div>
            `;
            this.elements.chatMessages.appendChild(messageEl);
        });
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight; // Scroll to bottom
    }

    /**
     * Toggles the chat panel visibility.
     */
    toggleChat() {
        if (this.elements.chatPanel) {
            this.elements.chatPanel.classList.toggle('active');
            // Optional: change FAB icon when panel is open/closed
            const fabIcon = this.elements.chatFab.querySelector('svg');
            if (this.elements.chatPanel.classList.contains('active')) {
                // Change to a close icon or different state for FAB
                // fabIcon.innerHTML = '<line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line>'; // Example: Close icon
            } else {
                // Reset to original FAB icon
                // fabIcon.innerHTML = '<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path><line x1="12" y1="12" x2="12" y2="12.01"></line> <line x1="8" y1="12" x2="8" y2="12.01"></line> <line x1="16" y1="12" x2="16" y2="12.01"></line>';
            }
        }
    }

    /**
     * Updates the save status display.
     * @param {string} text - The text to display (e.g., "Last saved: 10:30 AM").
     */
    updateSaveStatus(text) {
        this.elements.saveStatus.textContent = text;
    }

    /**
     * Shows a notification message.
     * @param {string} message - The message to display.
     * @param {string} type - The type of notification (e.g., 'success', 'error', 'warning').
     */
    showNotification(message, type = 'info') {
        utilShowNotification(message, type);
    }


    /**
     * Renders the selected idea in the outline step.
     */
    renderSelectedIdea() {
        const selectedIdea = this.stateManager.getStateValue('selectedIdea');
        if (!selectedIdea) return;

        let description = selectedIdea.description || `Article about ${selectedIdea.title}`;
        if (description.trim() === '') {
            description = `Article about ${selectedIdea.title}`;
        }

        this.elements.selectedIdeaContainer.innerHTML = `
            <div class="ai-idea-title">${selectedIdea.title}</div>
            <div class="ai-idea-description">${description}</div>
            <div class="ai-idea-meta">
                <div class="ai-idea-preset">${selectedIdea.preset}</div>
            </div>
        `;
    }

    /**
     * Toggles the visibility of the outline options panel.
     */
    toggleOutlineOptions() {
        this.elements.outlineOptionsContent.classList.toggle('collapsed');
        this.elements.toggleOutlineOptionsBtn.classList.toggle('collapsed');
    }

    /**
     * Switches the active outline tab and view.
     * @param {string} tabName - The name of the tab to switch to ('list' or 'visual').
     */
    switchOutlineTab(tabName) {
        this.elements.outlineTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        this.elements.outlineViews.forEach(view => {
            view.classList.toggle('active', view.id === `ai-outline-${tabName}-view`);
        });
    }

    /**
     * Updates the featured image preview.
     * @param {Object} image - The image object with a `url` property.
     */
    updateFeaturedImagePreview(image) {
        if (this.elements.featuredImagePreview && image && image.url) {
            this.elements.featuredImagePreview.innerHTML = `<img src="${image.url}" alt="Featured Image">`;
        } else if (this.elements.featuredImagePreview) {
            this.elements.featuredImagePreview.innerHTML = '<p>No featured image selected.</p>';
        }
    }
}
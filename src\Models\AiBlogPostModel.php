<?php

declare(strict_types=1);

namespace Brenzley\Models;

use <PERSON><PERSON>zley\Core\Database;
use PDO;

/**
 * Model for AI-generated blog posts (full content)
 */
class AiBlogPostModel
{
    private PDO $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Create a new AI-generated blog post
     *
     * @param array $data Blog post data
     * @return int|false The ID of the created blog post or false on failure
     */
    public function create(array $data): int|false
    {
        $sql = "INSERT INTO ai_blog_posts (
                    user_id, 
                    blog_idea_id, 
                    outline_id, 
                    title, 
                    content_html, 
                    provider, 
                    model, 
                    parameters
                ) VALUES (
                    :user_id, 
                    :blog_idea_id, 
                    :outline_id, 
                    :title, 
                    :content_html, 
                    :provider, 
                    :model, 
                    :parameters
                )";

        // Convert parameters array to JSON if needed
        if (isset($data['parameters']) && is_array($data['parameters'])) {
            $data['parameters'] = json_encode($data['parameters']);
        } else if (!isset($data['parameters'])) {
            $data['parameters'] = null; // Ensure it's null if not provided
        }


        $stmt = $this->db->prepare($sql);
        $success = $stmt->execute([
            ':user_id' => $data['user_id'],
            ':blog_idea_id' => $data['blog_idea_id'] ?? null,
            ':outline_id' => $data['outline_id'] ?? null,
            ':title' => $data['title'],
            ':content_html' => $data['content_html'] ?? null,
            ':provider' => $data['provider'] ?? null,
            ':model' => $data['model'] ?? null,
            ':parameters' => $data['parameters']
        ]);

        if ($success) {
            return (int)$this->db->lastInsertId();
        }

        return false;
    }

    /**
     * Find a blog post by ID
     *
     * @param int $id Blog post ID
     * @return array|false Blog post data or false if not found
     */
    public function findById(int $id): array|false
    {
        $sql = "SELECT * FROM ai_blog_posts WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        $post = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($post && isset($post['parameters']) && !empty($post['parameters'])) {
            $post['parameters'] = json_decode($post['parameters'], true);
        }
        
        return $post;
    }

    /**
     * Find all blog posts for a user
     *
     * @param int $userId User ID
     * @param int $limit Maximum number of results to return
     * @param int $offset Offset for pagination
     * @return array Blog posts
     */
    public function findByUserId(int $userId, int $limit = 10, int $offset = 0): array
    {
        $sql = "SELECT id, title, provider, model, created_at FROM ai_blog_posts 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update a blog post
     *
     * @param int $id Blog post ID
     * @param array $data Updated blog post data
     * @return bool Success status
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE ai_blog_posts SET ";
        $params = [];
        
        foreach ($data as $key => $value) {
            if (in_array($key, ['id', 'user_id', 'created_at', 'updated_at'])) { // Non-updatable fields
                continue;
            }
            
            if ($key === 'parameters' && is_array($value)) {
                $value = json_encode($value);
            }
            
            $sql .= "$key = :$key, ";
            $params[":$key"] = $value;
        }
        
        $sql = rtrim($sql, ', '); // Remove trailing comma
        $sql .= ", updated_at = NOW() WHERE id = :id"; // Add updated_at and WHERE clause
        $params[':id'] = $id;
        
        if (count($params) === 1) { // Only :id, means no actual fields to update
            return true; // Or false, depending on desired behavior for no-op updates
        }

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Delete a blog post
     *
     * @param int $id Blog post ID
     * @return bool Success status
     */
    public function delete(int $id): bool
    {
        $sql = "DELETE FROM ai_blog_posts WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Count total blog posts for a user
     *
     * @param int $userId User ID
     * @return int Total count
     */
    public function countByUserId(int $userId): int
    {
        $sql = "SELECT COUNT(*) FROM ai_blog_posts WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        return (int)$stmt->fetchColumn();
    }
}

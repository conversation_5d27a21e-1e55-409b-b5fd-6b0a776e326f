/**
 * AI Editor - Content Creation Process
 * Contains styles for ideas, outlines, and content generation workflow
 */

/* ========================================
   CONTENT CREATION PROCESS
   ======================================== */

/* Ideas Container */
.ai-ideas-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1.5rem;
    width: 100%;
    padding: 0 0.5rem;
}

.ai-idea-card-container {
    animation: fadeIn 0.5s ease;
    width: 100%;
}

.ai-idea-card {
    background: linear-gradient(135deg, var(--admin-bg), var(--admin-surface));
    border: 2px solid var(--admin-border);
    border-radius: 0.75rem;
    padding: 1.75rem;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    width: 100%;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

@media (max-width: 768px) {
    .ai-ideas-container {
        padding: 0;
        gap: 1.25rem;
    }
    
    .ai-idea-card {
        padding: 1.5rem;
        border-radius: 0.5rem;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .ai-idea-card {
        padding: 1.25rem;
        gap: 0.875rem;
    }
}

.ai-idea-card:hover {
    border-color: var(--ai-primary);
    box-shadow: 0 8px 25px rgba(var(--ai-primary-rgb), 0.15), 0 3px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-4px) scale(1.02);
}

.ai-idea-card.selected,
.ai-idea-card:hover {
    border-color: var(--ai-primary);
}

.ai-idea-card.selected {
    background: linear-gradient(135deg, rgba(var(--ai-primary-rgb), 0.08), rgba(var(--ai-primary-rgb), 0.04));
    border-color: var(--ai-primary);
    box-shadow: 0 4px 20px rgba(var(--ai-primary-rgb), 0.2), 0 0 0 1px rgba(var(--ai-primary-rgb), 0.1);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .ai-idea-card:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 4px 15px rgba(var(--ai-primary-rgb), 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    }
    
    .ai-idea-card.selected {
        transform: translateY(-1px);
        box-shadow: 0 2px 12px rgba(var(--ai-primary-rgb), 0.15);
    }
}

.ai-idea-card.generating {
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 1px rgba(var(--ai-primary-rgb), 0.3);
    animation: pulse 2s infinite ease-in-out;
}

.ai-idea-card-number {
    position: absolute;
    top: -10px;
    left: -10px;
    background-color: var(--ai-primary);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: var(--ai-shadow-sm);
}

.ai-idea-title {
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--admin-text-primary);
    margin: 0;
    line-height: 1.4;
    position: relative;
    padding-bottom: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.ai-idea-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3rem;
    height: 3px;
    background: linear-gradient(90deg, var(--ai-primary), var(--ai-primary-dark));
    border-radius: 2px;
    transition: width 0.3s ease;
}

.ai-idea-card:hover .ai-idea-title::after,
.ai-idea-card.selected .ai-idea-title::after {
    width: 4rem;
}

.ai-idea-description {
    font-size: 0.9375rem;
    color: var(--admin-text-secondary);
    margin: 0;
    line-height: 1.6;
    flex-grow: 1;
}

@media (max-width: 768px) {
    .ai-idea-title {
        font-size: 1.125rem;
        padding-bottom: 0.5rem;
    }
    
    .ai-idea-title::after {
        width: 2.5rem;
        height: 2px;
    }
    
    .ai-idea-card:hover .ai-idea-title::after,
    .ai-idea-card.selected .ai-idea-title::after {
        width: 3rem;
    }
    
    .ai-idea-description {
        font-size: 0.875rem;
        line-height: 1.5;
    }
}

.ai-idea-target-audience {
    font-size: 0.8125rem;
    color: var(--admin-text-secondary);
    margin: 0;
    padding: 0.5rem 0.75rem;
    background-color: rgba(var(--admin-border-rgb), 0.3);
    border-radius: 0.25rem;
    border-left: 3px solid var(--ai-primary);
}

.ai-idea-key-points {
    font-size: 0.8125rem;
    color: var(--admin-text-secondary);
    margin: 0;
}

.ai-idea-key-points ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.25rem;
}

.ai-idea-key-points li {
    margin-bottom: 0.375rem;
    position: relative;
}

.ai-idea-key-points li::marker {
    color: var(--ai-primary);
}

.ai-idea-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--admin-text-secondary);
    margin-top: auto;
}

.ai-idea-preset {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background-color: var(--ai-primary-light);
    color: var(--ai-primary);
    border-radius: 0.25rem;
    font-weight: 500;
    text-transform: capitalize;
    letter-spacing: 0.02em;
}

/* Selected Idea Display (e.g., above outline options) */
.ai-selected-idea {
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Outline Options */
.ai-outline-options {
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.ai-outline-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--admin-surface);
    border-bottom: 1px solid var(--admin-border);
}

.ai-outline-options-content {
    padding: 1rem;
    transition: max-height 0.3s, opacity 0.3s;
}

.ai-outline-options-content.collapsed {
    max-height: 0;
    padding: 0 1rem;
    opacity: 0;
    overflow: hidden;
}

/* Outline Tabs */
.ai-outline-tabs {
    display: flex;
    border-bottom: 1px solid var(--admin-border);
    margin-bottom: 1rem;
}

.ai-outline-tab {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--admin-text-secondary);
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: color 0.2s, border-color 0.2s;
}

.ai-outline-tab:hover {
    color: var(--admin-text-primary);
}

.ai-outline-tab.active {
    color: var(--ai-primary);
    border-bottom-color: var(--ai-primary);
}

.ai-outline-view {
    display: none;
}

.ai-outline-view.active {
    display: block;
}

/* Outline Container */
.ai-outline-container {
    /* For list view */
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.ai-outline-items-container {
    /* Used by streaming JS */
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.ai-outline-item {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    background-color: var(--admin-bg);
    cursor: move;
    position: relative;
    transition: all 0.2s ease;
}

.ai-outline-item:last-child {
    margin-bottom: 0;
}

.ai-outline-item.dragging {
    opacity: 0.5;
    background-color: var(--ai-primary-light);
}

.ai-outline-item.generating::before {
    content: '';
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--ai-primary);
    animation: blink 1s infinite;
}

.ai-outline-heading {
    font-weight: 600;
    font-size: 1rem;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.ai-outline-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--ai-primary-light);
    color: var(--ai-primary);
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 0.5rem;
}

.ai-outline-description {
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    padding-left: 2rem;
    /* Align with heading text, not number */
    margin-bottom: 0.5rem;
}

.ai-outline-key-points {
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    padding-left: 2rem;
    margin-bottom: 0.5rem;
}

.ai-outline-key-points ul {
    margin: 0.25rem 0 0 0;
    padding-left: 1.25rem;
}

.ai-outline-key-points li {
    margin-bottom: 0.25rem;
    position: relative;
}

.ai-outline-key-points li::marker {
    color: var(--ai-primary);
}

/* Subsections in Outline List View */
.ai-outline-subsections {
    background-color: rgba(var(--admin-border-rgb), 0.2);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    margin-left: 2rem;
}

.ai-outline-subsections h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text-primary);
    margin: 0 0 0.75rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(var(--admin-border-rgb), 0.5);
}

.ai-outline-subsection {
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed rgba(var(--admin-border-rgb), 0.5);
}

.ai-outline-subsection:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.ai-outline-subsection-heading {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.ai-outline-subsection-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.25rem;
    height: 1.25rem;
    background-color: rgba(var(--ai-primary-rgb), 0.8);
    color: white;
    border-radius: 0.25rem;
    font-size: 0.6875rem;
    font-weight: 600;
    margin-right: 0.5rem;
    padding: 0 0.25rem;
    flex-shrink: 0;
}

.ai-outline-subsection-description {
    font-size: 0.8125rem;
    color: var(--admin-text-secondary);
    line-height: 1.5;
}

.ai-outline-controls {
    position: absolute;
    right: 0.5rem;
    top: 0.5rem;
    display: flex;
    gap: 0.25rem;
}

.ai-outline-control-btn {
    background: none;
    border: none;
    color: var(--admin-text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
}

.ai-outline-control-btn:hover {
    color: var(--admin-text-primary);
}

/* Outline Tree View (Map) */
.ai-outline-map-container {
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    height: 500px;
    overflow: auto;
    position: relative;
}

.ai-outline-tree {
    position: relative;
    padding: 1rem;
    width: 100%;
    /* height: 100%; /* Removed to allow content to define height */
    overflow: visible;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* Center the root node */
}

.ai-tree-node {
    background-color: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    /* Spacing between sibling nodes */
    box-shadow: var(--ai-shadow-sm);
    /* Softer shadow */
    transition: all 0.2s ease-in-out;
}

.ai-tree-node:hover {
    box-shadow: var(--ai-shadow);
    /* Slightly larger shadow on hover */
    border-color: var(--ai-primary);
}

.ai-tree-node-content {
    padding: 0.75rem;
    display: flex;
    align-items: flex-start;
    /* Align items to the start for number and text */
    gap: 0.75rem;
}

.ai-tree-node-number {
    /* Shared with outline list view */
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    /* Ensure consistent width */
    height: 24px;
    background-color: var(--ai-primary-light);
    color: var(--ai-primary);
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
    /* Prevent shrinking */
}

.ai-tree-node-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--admin-text-primary);
    /* margin-bottom: 0.25rem; /* Removed for tighter look if no description */
}

.ai-tree-node-description {
    /* Not used in current map, but good to have */
    font-size: 0.75rem;
    color: var(--admin-text-secondary);
}

/* Root Node (Title) */
.ai-tree-root {
    background-color: var(--ai-primary-light);
    border-color: var(--ai-primary);
    box-shadow: 0 2px 8px rgba(var(--ai-primary-rgb), 0.2);
    margin-bottom: 1.5rem;
    max-width: 400px;
    /* Constrain width of root */
    width: auto;
    /* Allow it to shrink to content */
    align-self: center;
    /* Ensure it's centered if tree is wider */
}

.ai-tree-root .ai-tree-node-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.ai-tree-root .ai-tree-node-title {
    color: var(--ai-primary);
    font-size: 1rem;
}

/* Section Nodes */
.ai-tree-section {
    background-color: var(--admin-surface);
    max-width: 350px;
    /* Constrain width */
    width: auto;
    align-self: flex-start;
    /* Align to the start of the children container */
}

/* Subsection Nodes */
.ai-tree-subsection {
    background-color: var(--admin-bg);
    font-size: 0.875rem;
    /* Slightly smaller */
    max-width: 300px;
    width: auto;
    align-self: flex-start;
}

/* Sub-subsection Nodes */
.ai-tree-subsubsection {
    background-color: var(--admin-bg);
    font-size: 0.8125rem;
    max-width: 280px;
    width: auto;
    align-self: flex-start;
}

/* Tree Structure Lines */
.ai-tree-children {
    display: flex;
    flex-direction: column;
    width: 100%;
    /* Take full width of parent node for alignment */
    padding-left: 1.5rem;
    /* Indentation for children */
    position: relative;
    /* For pseudo-elements */
    align-items: flex-start;
    /* Align children to the start */
}

.ai-tree-branch {
    position: relative;
    padding-left: 1.5rem;
    /* Space for the line from parent */
    /* border-left: 2px solid var(--admin-border); /* Vertical line connecting to parent */
    /* margin-left: 0.5rem; /* Small offset for the node itself from the line */
    width: 100%;
    /* Ensure branch takes width for its children alignment */
}

.ai-tree-root>.ai-tree-children>.ai-tree-branch {
    border-left: none;
    /* No vertical line directly from root's children container */
    padding-left: 0;
}

.ai-tree-branch::before {
    /* Horizontal line from parent to child's vertical line */
    content: '';
    position: absolute;
    top: 1.1em;
    /* Align with center of node number/icon */
    left: -1.5rem;
    /* Start from where parent's vertical line would be */
    width: 1.5rem;
    /* Length of horizontal connector */
    height: 2px;
    background-color: var(--admin-border);
}

.ai-tree-root>.ai-tree-children>.ai-tree-branch::before {
    /* For direct children of root, the line comes from center of root */
    left: 50%;
    transform: translateX(-100%);
    /* Adjust to connect from root center to child start */
    top: -0.75rem;
    /* From bottom of root to top of child branch */
    width: 2px;
    height: 0.75rem;
    /* Vertical connector */
}

.ai-tree-branch:last-child>.ai-tree-children {
    /* If this is the last child, its own vertical line shouldn't extend past its content */
    /* This is complex to do perfectly with pure CSS for dynamic heights */
}

/* Table of Contents Section */
.ai-toc-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.ai-toc-preview-container,
.ai-toc-code-container {
    background-color: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.ai-toc-preview {
    margin-top: 0.5rem;
}

.ai-toc-preview ul,
.ai-toc-preview ol {
    padding-left: 1.5rem;
    margin: 0.5rem 0;
    list-style-position: inside;
}

.ai-toc-preview li {
    margin-bottom: 0.25rem;
}

.ai-toc-preview a {
    color: var(--ai-primary);
    text-decoration: none;
}

.ai-toc-preview a:hover {
    text-decoration: underline;
}

.ai-toc-preview .toc-h2 {
    font-weight: 600;
}

.ai-toc-preview .toc-h3 {
    padding-left: 1rem;
    font-weight: normal;
}

.ai-toc-preview .toc-h4 {
    padding-left: 2rem;
    font-weight: normal;
    font-size: 0.875rem;
}

.ai-toc-collapsible .toc-toggle {
    cursor: pointer;
    user-select: none;
    margin-right: 0.25rem;
}

.ai-toc-collapsible .toc-children {
    display: none;
}

.ai-toc-collapsible .toc-expanded>.toc-children {
    display: block;
}
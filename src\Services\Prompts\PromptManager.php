<?php

declare(strict_types=1);

namespace Brenzley\Services\Prompts;

/**
 * Manages all AI prompts for the application.
 * Provides standardized system and user prompts for various generation tasks.
 */
class PromptManager
{
    // --- Full Article Content Prompts (from AiBlogPostController) ---

    /**
     * Gets the system and user prompts for generating a full article.
     * @param string $title
     * @param array $outlineData
     * @param string $ideaPreset
     * @return array ['system' => string, 'user' => string]
     */
    public static function getFullArticlePrompts(string $title, array $outlineData, string $ideaPreset): array
    {
        $systemPrompt = <<<EOT
<ROLE>
You are a Senior Content Strategist and SEO Specialist. Your expertise lies in transforming structured outlines into comprehensive, engaging, and well-written articles that are optimized for readability and search engines. You write in clear, standard Markdown.
</ROLE>

<TASK>
Your task is to generate a full article based on the provided <CONTEXT>. You must follow all rules defined in <FORMATTING_RULES> and <IMAGE_PLACEMENT>.

**SPECIAL INSTRUCTIONS FOR THIS TASK**: For this specific request, you must generate a comprehensive test article that showcases all available markdown and image formatting options. Use a variety of headings, lists (ordered and unordered), blockquotes, inline code, and text formatting. Crucially, you must include at least one example of every single image layout type specified in the `<IMAGE_PLACEMENT>` rules.
</TASK>

<CONTEXT>
The user will provide the article's Title, a detailed Outline, and a desired Tone/Preset. You must expand on each point in the outline to create the full article.
</CONTEXT>

<FORMATTING_RULES>
1.  **Output Format**: Your entire response must be in standard Markdown.
2.  **Headings**: Use Markdown headings (`##`, `###`, etc.) to structure the article according to the provided outline. **DO NOT** use H1 (`#`) as the main article title is handled separately.
3.  **Content Flow**: Ensure a logical and smooth flow between sections. Each paragraph should be well-crafted and contribute to the overall narrative.
4.  **Engagement**: Write in an engaging and informative manner, expanding on the key points from the outline with relevant details, examples, or explanations.
5.  **Tone**: Strictly adhere to the Tone/Preset specified in the user's prompt.
</FORMATTING_RULES>

<IMAGE_PLACEMENT>
To insert an image, you must use the following XML-style tag format. This is a strict requirement.

**IMPORTANT: All images are now manual uploads only. AI image generation has been removed.**

**Core Structure:**
<IMAGE alt_text="[A concise and descriptive alt text for accessibility]" layout="[The desired layout]">
  <description>
    [A clear description for a human user of what kind of image to upload]
  </description>
  <additional_content>
    [Optional: Text content for layouts that support it, like 'text_overlay' or 'left'/'right'.]
  </additional_content>
</IMAGE>

**Tag Details:**
1.  **`<IMAGE>` Tag**: The main container for all image information.
    *   `alt_text`: **Required**. Provide a brief, descriptive alt text for the image. For galleries, describe the gallery as a whole (e.g., "A gallery of three images showing the product from different angles.").
    *   `layout`: **Required**. Specifies the visual layout. Supported values are:
        - `standard`: A standard, full-width image.
        - `wide`: A wider, panoramic image.
        - `square`: A 1:1 aspect ratio image.
        - `left`: Image floated to the left. The text in `<additional_content>` will wrap around it.
        - `right`: Image floated to the right. The text in `<additional_content>` will wrap around it.
        - `text_overlay`: An image with the text from `<additional_content>` overlaid on top.
        - `gallery-2-item`: A two-image gallery. **Must contain exactly two** `<description>` tags.
        - `gallery-3-item`: A three-image gallery. **Must contain exactly three** `<description>` tags.

2.  **`<description>` Tag(s)**:
    *   Use `<description>` for manual image uploads. The content should be a clear description for a human user of what kind of image to upload.
    *   For single-image layouts, use exactly one `<description>` tag.
    *   For gallery layouts, use the exact number of `<description>` tags as specified in the layout name (e.g., `gallery-3-item` must contain exactly three `<description>` tags).

3.  **`<additional_content>` Tag**:
    *   This tag is **only used** for layouts: `left`, `right`, and `text_overlay`. For all other layouts, this tag should be **omitted**.
    *   For `left`/`right` layouts, this tag should contain the paragraph(s) of text that should appear directly next to the floated image.
    *   For `text_overlay` layout, this tag should contain the concise text to be displayed on top of the image.

**Examples:**

*   **Example 1: Standard Manual Image**
    <IMAGE alt_text="A mountain landscape at sunrise showing misty valleys." layout="standard">
      <description>
        Upload a high-quality photograph of a mountain landscape at sunrise, featuring misty valleys and warm golden lighting.
      </description>
    </IMAGE>

*   **Example 2: Floated Manual Image with Text**
    <IMAGE alt_text="A diagram explaining the user authentication workflow." layout="left">
      <description>
        Upload a clear diagram or flowchart illustrating the steps of the user authentication workflow.
      </description>
      <additional_content>
        This paragraph explains the diagram in detail. It will appear to the right of the image, allowing the user to view both simultaneously. This text is wrapped within the additional_content tag.
      </additional_content>
    </IMAGE>

*   **Example 3: 2-Item Manual Gallery**
    <IMAGE alt_text="A gallery showing two different views of a modern kitchen." layout="gallery-2-item">
      <description>
        Upload a wide shot of a modern, minimalist kitchen with white cabinets, marble island, and stainless steel appliances with natural lighting.
      </description>
      <description>
        Upload a close-up shot of kitchen appliances or cooking area, showing details like the stove, countertop, or cooking utensils.
      </description>
    </IMAGE>
</IMAGE_PLACEMENT>
EOT;

        $outlineText = "";
        foreach ($outlineData as $section) {
            $outlineText .= "# " . ($section['heading'] ?? 'Untitled Section') . "\n";
            if (!empty($section['description'])) {
                $outlineText .= strip_tags((string)$section['description']) . "\n";
            }
            if (!empty($section['key_points']) && is_array($section['key_points'])) {
                foreach ($section['key_points'] as $kp) {
                    $outlineText .= "- " . strip_tags((string)$kp) . "\n";
                }
            }
            if (!empty($section['subsections']) && is_array($section['subsections'])) {
                foreach ($section['subsections'] as $subsection) {
                    $outlineText .= "## " . ($subsection['heading'] ?? 'Untitled Subsection') . "\n";
                    if (!empty($subsection['description'])) {
                        $outlineText .= strip_tags((string)$subsection['description']) . "\n";
                    }
                    if (!empty($subsection['key_points']) && is_array($subsection['key_points'])) {
                        foreach ($subsection['key_points'] as $kp) {
                            $outlineText .= "  - " . strip_tags((string)$kp) . "\n";
                        }
                    }
                }
            }
            $outlineText .= "\n";
        }

        $userPrompt = "Article Title: {$title}\n\nArticle Outline:\n{$outlineText}\n\nPreset/Tone: {$ideaPreset}\n\nPlease generate the full article content now, following all rules and formats specified in the system prompt.";

        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    // --- Prompts formerly in ApiService / AbstractContentGenerator ---

    /**
     * Gets the prompts for blog idea generation.
     * @param string $topic
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogIdeaPrompts(string $topic, array $parameters = []): array
    {
        $audience = $parameters['audience'] ?? 'general readers';
        $goal = $parameters['goal'] ?? 'educate';
        $numIdeas = $parameters['num_ideas'] ?? 5; // Added from AiBlogIdeaController's createBlogIdeaPrompt

        $systemPrompt = "You are an expert content strategist and idea generator for blog articles. Your task is to help content creators generate high-quality, engaging blog post ideas based on their target audience, industry, and goals.
You excel at creating engaging, SEO-friendly blog post ideas that resonate with target audiences.

Follow these guidelines:
1. Generate ideas that are specific, not generic.
2. Each idea should have a clear angle or hook.
3. Ideas should be tailored to the specified audience.
4. Provide a mix of timely and evergreen content ideas.
5. Include ideas with different content formats (how-to, list, opinion, etc.).
6. Avoid politically charged or controversial topics unless specifically requested.

Your responses should be structured, concise, and directly usable by the content creator.";

        // User prompt structure from AiBlogIdeaController's createBlogIdeaPrompt
        $userPrompt = "Generate {$numIdeas} concise, focused blog post ideas about {$topic} for {$audience}.

IMPORTANT: Follow this EXACT format for EACH idea:

IDEA #[number]
Title: [A catchy, SEO-friendly title - max 10 words]
Description: [2-3 sentences describing the blog post idea - be specific and compelling]
Target Audience: [Brief description of the target audience - demographics, interests, pain points]
Key Points:
- [Key point 1 - one short sentence]
- [Key point 2 - one short sentence]
- [Key point 3 - one short sentence]

Separate each idea with a line break.

DO NOT:
- Write a full outline or article
- Include introduction, body, or conclusion sections
- Add any additional sections not requested
- Exceed the requested format
- Use placeholder text

Each idea should be specific, interesting, and valuable to readers. Focus on {$goal}.
Make each idea unique and different from the others.";

        if (isset($parameters['existing_content'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - EXISTING CONTENT ON SITE:\n{$parameters['existing_content']}";
        }
        if (isset($parameters['competitors'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - COMPETITORS:\n{$parameters['competitors']}";
        }
        if (isset($parameters['keywords'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - TARGET KEYWORDS:\n{$parameters['keywords']}";
        }
        
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for blog outline generation.
     * @param string $title
     * @param string $description
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogOutlinePrompts(string $title, string $description, array $parameters = []): array
    {
        $outlineDepth = $parameters['outline_depth'] ?? 'h2'; // h2 implies main sections, 'h3' or other could imply subsections
        $outlineStyle = $parameters['outline_style'] ?? 'informative';
        $preset = $parameters['preset'] ?? 'informative'; // Could be 'blog_post', 'listicle', 'how_to_guide' etc.

        $systemPrompt = "You are an expert content strategist and outline creator for blog articles. Your task is to create a well-structured, comprehensive outline for a blog post based on the provided title and description.
You excel at creating well-structured, comprehensive blog post outlines that cover topics thoroughly and are SEO-friendly.

Follow these guidelines:
1. Create a clear, logical structure with a natural flow.
2. Include an introduction and conclusion.
3. Organize main points in a coherent sequence.
4. Use descriptive, engaging headings that hook the reader and are good for SEO.
5. Include brief descriptions for each section outlining its purpose and content.
6. Add subsections where appropriate to enhance detail and readability (especially if outline_depth suggests it).
7. Format the outline in a consistent, easy-to-read structure.
8. Focus on creating content that will engage and hook readers.

IMPORTANT: Follow this EXACT format for EACH section:

SECTION #[number]: [Section Title]
Description: [Brief description of this section's content and purpose]
Key Points:
- [Key point 1 - concise and actionable]
- [Key point 2 - concise and actionable]
- [Key point 3 - concise and actionable]

For sections that benefit from subsections (if outline_depth is not 'h2' or if naturally fitting), add them in this format:

SUBSECTION #[section number].[subsection number]: [Subsection Title]
Description: [Brief description of this subsection's content and purpose]

Separate each main section with a double line break.

DO NOT:
- Write a full article.
- Exceed the requested format.
- Use placeholder text like '[Your content here]'.
- Force subsections where they don't naturally fit if outline_depth is 'h2'.";

        $userPrompt = "I need to create an engaging, reader-focused blog post with the following details:

TITLE: {$title}
DESCRIPTION: {$description}
DESIRED STYLE: {$outlineStyle}
CONTENT TYPE/PRESET: {$preset}";

        if ($outlineDepth !== 'h2') {
            $userPrompt .= "\nOUTLINE DEPTH: Please include detailed subsections (e.g., H2s and H3s).";
        } else {
            $userPrompt .= "\nOUTLINE DEPTH: Focus on main sections (e.g., H2s), subsections can be included if they are very natural and add significant value.";
        }
        $userPrompt .= "\n\nPlease create a detailed outline following the exact format specified. Include as many sections as needed to comprehensively cover the topic, with a natural introduction and conclusion. Focus on creating a structure that will hook readers from the start and keep them engaged throughout the article.";
        
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for blog section generation.
     * @param string $sectionTitle
     * @param string $articleContext
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogSectionPrompts(string $sectionTitle, string $articleContext, array $parameters = []): array
    {
        $tone = $parameters['tone'] ?? 'informative';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a concise section (about 150-200 words).',
            'long' => 'Write a comprehensive section (about 400-500 words).',
            default => 'Write a balanced section (about 250-350 words).'
        };
        $toneInstructions = "Use a {$tone} tone.";

        $systemPrompt = "You are an expert content writer specializing in creating engaging blog content. You excel at writing well-structured, informative sections that flow naturally within a larger article and maintain a consistent tone.";

        $userPrompt = <<<EOT
Write a blog post section with the title "{$sectionTitle}".

Context from the rest of the post (previous sections or overall outline):
{$articleContext}

{$lengthInstructions}
{$toneInstructions}

Include relevant examples, data points, or anecdotes where appropriate. Make the content engaging, informative, and valuable to readers.
The section should be written in HTML format, using <p> tags for paragraphs, and <strong> or <em> for emphasis if needed. Do not include the section title heading itself in the output.

Ensure the section:
1. Starts with a smooth transition from the previous content (if applicable from context).
2. Thoroughly covers the topic indicated by the section title.
3. Uses clear, concise language with good readability.
4. Ends with a transition to the next section (if applicable from context).
5. Maintains consistency with the overall post style and tone.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for full blog post generation (non-streaming context).
     * This combines the full article system prompt with a user prompt for the post.
     * @param string $title
     * @param string $outline // This is expected to be a string representation of the outline
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getFullBlogPostPrompts(string $title, string $outline, array $parameters = []): array
    {
        $systemPrompt = self::getSystemPromptForFullArticle(); // Reuse the detailed system prompt

        $tone = $parameters['tone'] ?? 'informative';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a concise blog post (about 600-800 words).',
            'long' => 'Write a comprehensive blog post (about 1500-2000 words).',
            default => 'Write a balanced blog post (about 1000-1200 words).'
        };
        $toneInstructions = "Use a {$tone} tone.";

        $userPrompt = <<<EOT
Write a complete blog post with the following title and outline:

Title: {$title}

Outline:
{$outline}

{$lengthInstructions}
{$toneInstructions}

Include an engaging introduction that hooks the reader, well-structured body sections that follow the outline, and a conclusion that summarizes key points and includes a call to action.
Follow all instructions in the system prompt regarding HTML formatting and image tags.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for image caption generation.
     * @param string $imageDescription
     * @param string $context
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getImageCaptionPrompts(string $imageDescription, string $context, array $parameters = []): array
    {
        $style = $parameters['style'] ?? 'descriptive';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a brief caption (1 sentence).',
            'long' => 'Write a detailed caption (3-4 sentences).',
            default => 'Write a standard caption (1-2 sentences).'
        };
        $styleInstructions = match($style) {
            'humorous' => 'Use a light, humorous tone.',
            'technical' => 'Use precise, technical language.',
            'engaging' => 'Make the caption engaging and attention-grabbing.',
            default => 'Be clear and descriptive.'
        };

        $systemPrompt = "You are a professional caption writer specializing in creating engaging image captions. You excel at crafting captions that enhance the viewer's understanding and appreciation of images.";
        
        $userPrompt = <<<EOT
Write a caption for an image with the following description:

Image: {$imageDescription}

Context where the image appears:
{$context}

{$lengthInstructions}
{$styleInstructions}

The caption should be relevant to both the image and the context.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for image alt text generation.
     * @param string $imageDescription
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getAltTextPrompts(string $imageDescription, array $parameters = []): array
    {
        $systemPrompt = "You are an accessibility expert specializing in creating effective alt text for images. You excel at writing concise, descriptive alt text that helps visually impaired users understand images.";
        
        $userPrompt = <<<EOT
Create concise, descriptive alt text for an image with the following description:

Image: {$imageDescription}

Guidelines for the alt text:
1. Be concise (preferably under 125 characters)
2. Be descriptive and specific
3. Convey the image's purpose and content
4. Don't start with "Image of" or "Picture of"
5. Focus on key details that matter for understanding the content

The alt text should help visually impaired users understand what the image shows and its relevance.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for improving content.
     * @param string $content
     * @param string $instructions
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getImproveContentPrompts(string $content, string $instructions, array $parameters = []): array
    {
        $systemPrompt = "You are an expert content editor with a keen eye for improving writing. You excel at enhancing content while maintaining its original voice and message.";
        
        $userPrompt = <<<EOT
Improve the following content according to these instructions:

Instructions: {$instructions}

Original Content:
{$content}

Please maintain the original meaning and key points while making the requested improvements.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for generating a response to a query.
     * @param string $query
     * @param string $context
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getGenerateResponsePrompts(string $query, string $context, array $parameters = []): array
    {
        $tone = $parameters['tone'] ?? 'helpful';
        
        $systemPrompt = "You are a knowledgeable assistant specializing in providing accurate, helpful responses. You excel at synthesizing information and presenting it in a clear, concise manner.";
        
        $userPrompt = <<<EOT
Answer the following user query based on the provided context:

User Query: {$query}

Context Information:
{$context}

Please provide a {$tone}, accurate response based on the context information. If the context doesn't contain enough information to fully answer the query, acknowledge this limitation in your response.
Your response should be:
1. Directly relevant to the user's query
2. Based on the provided context information
3. Clear and easy to understand
4. Formatted for readability with paragraphs and bullet points as needed
5. Comprehensive but concise
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }
}
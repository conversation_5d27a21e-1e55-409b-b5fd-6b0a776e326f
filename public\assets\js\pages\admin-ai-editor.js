import { StateManager } from './admin-ai-editor/core/StateManager.js';
import { UIManager } from './admin-ai-editor/core/UIManager.js';
import { IdeaHandler } from './admin-ai-editor/handlers/IdeaHandler.js';
import { OutlineHandler } from './admin-ai-editor/handlers/OutlineHandler.js';
import { ContentHandler } from './admin-ai-editor/handlers/ContentHandler.js';
import { ContentEditorHandler } from './admin-ai-editor/handlers/ContentEditorHandler.js';
import { TableOfContentsHandler } from './admin-ai-editor/handlers/TableOfContentsHandler.js';
import { ChatFeature } from './admin-ai-editor/features/ChatFeature.js';
import { ImageFeature } from './admin-ai-editor/features/ImageFeature.js';
import { SavePublishFeature } from './admin-ai-editor/features/SavePublishFeature.js';
import { MobileUtils } from './admin-ai-editor/utils/MobileUtils.js';
import { showNotification } from './admin-ai-editor/utils/EditorUtils.js';

/**
 * AI-Powered Article Creator
 * A streamlined, step-by-step process for creating AI-generated articles
 */
class AIArticleCreator {
    constructor() {
        this.stateManager = new StateManager();
        this.uiManager = new UIManager(this.stateManager);
        this.chatFeature = new ChatFeature(this.uiManager, this.stateManager);
        this.mobileUtils = new MobileUtils();
        
        this.contentEditorHandler = new ContentEditorHandler(this.uiManager, this.stateManager);
        this.tocHandler = new TableOfContentsHandler(this.uiManager, this.stateManager);
        this.imageFeature = new ImageFeature(this.uiManager, this.stateManager, this.chatFeature);

        this.ideaHandler = new IdeaHandler(this.uiManager, this.stateManager, this.chatFeature);
        this.outlineHandler = new OutlineHandler(this.uiManager, this.stateManager, this.chatFeature, this.tocHandler);
        this.contentHandler = new ContentHandler(this.uiManager, this.stateManager, this.chatFeature, this.contentEditorHandler, this.imageFeature);
        
        this.savePublishFeature = new SavePublishFeature(this.uiManager, this.stateManager);
    }

    init() {
        this.uiManager.updateStepUI(this.stateManager.getStateValue('currentStep'));
        this.updateHeaderVisibility(this.stateManager.getStateValue('currentStep'));
        this.chatFeature.init();
        this.contentEditorHandler.init();
        this.tocHandler.init();
        this.imageFeature.init();
        this.savePublishFeature.init();
        this.mobileUtils.init();

        this.ideaHandler.init();
        this.outlineHandler.init();
        this.contentHandler.init();
        
        // Listener for step changes
        document.addEventListener('aieditor:stepChange', (event) => {
            const newStep = event.detail.step;
            this.goToStep(newStep);
        });

        // Mobile-specific optimizations
        if (this.mobileUtils.isMobileDevice()) {
            this.mobileUtils.optimizeScrolling();
            this.setupMobileOptimizations();
        }

        console.log('AI Article Creator initialized (Modular)');
        this.chatFeature.addAssistantMessage("Welcome! Let's create an amazing article.");
        // Initial UI setup based on the first step
        this.performStepSpecificActions(this.stateManager.getStateValue('currentStep'));
    }

    goToStep(step) {
        this.stateManager.setCurrentStep(step);
        this.uiManager.updateStepUI(step);
        this.updateHeaderVisibility(step);
        this.performStepSpecificActions(step);
        this.chatFeature.addAssistantMessage(this.chatFeature.getStepWelcomeMessage(step));
    }

    updateHeaderVisibility(step) {
        const header = document.querySelector('.ai-editor-header');
        if (header) {
            if (step === 'content') {
                // Show header for content step
                header.classList.add('show-header');
            } else {
                // Hide header for idea, outline, and seo steps
                header.classList.remove('show-header');
            }
        }
    }

    performStepSpecificActions(step) {
        switch (step) {
            case 'idea':
                // IdeaHandler's init should cover this, or add specific UI updates if needed
                break;
            case 'outline':
                this.uiManager.renderSelectedIdea(); // Renders the selected idea at the top
                if (!this.stateManager.getStateValue('outline')?.length) {
                    this.outlineHandler.generateOutline();
                } else {
                    this.outlineHandler.renderOutline();
                    this.outlineHandler.renderOutlineMap();
                }
                break;
            case 'content':
                 // Auto-set title from selected idea if not already set
                if (!this.stateManager.getStateValue('title') && this.stateManager.getStateValue('selectedIdea')?.title) {
                    this.contentEditorHandler.setTitle(this.stateManager.getStateValue('selectedIdea').title);
                }
                if (!this.stateManager.getStateValue('content')) {
                    this.contentHandler.generateContent();
                }
                break;
            case 'seo':
                // SEO functionality is currently unavailable
                break;
        }
    }

    setupMobileOptimizations() {
        // Add haptic feedback to important buttons
        const importantButtons = document.querySelectorAll('#ai-generate-ideas, #ai-approve-outline, #ai-generate-initial-content');
        importantButtons.forEach(button => {
            this.mobileUtils.addHapticFeedback(button, 'medium');
        });

        // Add light haptic feedback to regular buttons
        const regularButtons = document.querySelectorAll('.ai-btn:not(#ai-generate-ideas):not(#ai-approve-outline):not(#ai-generate-initial-content)');
        regularButtons.forEach(button => {
            this.mobileUtils.addHapticFeedback(button, 'light');
        });

        // Optimize touch scrolling for content areas
        const scrollAreas = document.querySelectorAll('.ai-process-content, .ai-ideas-container, .ai-outline-container');
        scrollAreas.forEach(area => {
            area.style.webkitOverflowScrolling = 'touch';
            area.style.overscrollBehavior = 'contain';
        });

        // Add mobile-specific event listeners
        this.setupMobileGestures();
    }

    setupMobileGestures() {
        // Add swipe gestures for step navigation on mobile
        if (this.mobileUtils.isMobileDevice()) {
            let startX = 0;
            let startY = 0;
            
            const processContent = document.querySelector('.ai-process-content');
            if (processContent) {
                processContent.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                });

                processContent.addEventListener('touchend', (e) => {
                    const endX = e.changedTouches[0].clientX;
                    const endY = e.changedTouches[0].clientY;
                    const diffX = startX - endX;
                    const diffY = startY - endY;

                    // Only trigger swipe if horizontal movement is greater than vertical
                    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                        if (diffX > 0) {
                            // Swipe left - next step
                            this.handleSwipeNavigation('next');
                        } else {
                            // Swipe right - previous step
                            this.handleSwipeNavigation('prev');
                        }
                    }
                });
            }
        }
    }

    handleSwipeNavigation(direction) {
        const currentStep = this.stateManager.getStateValue('currentStep');
        const steps = ['idea', 'outline', 'content', 'seo'];
        const currentIndex = steps.indexOf(currentStep);

        let newIndex;
        if (direction === 'next' && currentIndex < steps.length - 1) {
            newIndex = currentIndex + 1;
        } else if (direction === 'prev' && currentIndex > 0) {
            newIndex = currentIndex - 1;
        }

        if (newIndex !== undefined) {
            this.goToStep(steps[newIndex]);
            // Show a subtle notification about the step change
            showNotification(`Moved to ${steps[newIndex]} step`, 'info', 2000);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.ai-editor-container')) { // Check if on the AI editor page
        const aiArticleCreator = new AIArticleCreator();
        aiArticleCreator.init();
        window.aiArticleCreator = aiArticleCreator; // Optional: for debugging
    }
});

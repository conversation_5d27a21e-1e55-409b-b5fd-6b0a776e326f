<?php
/**
 * AI-Powered Article Creation
 * A streamlined, step-by-step process for creating AI-generated articles
 */

// Get old input and errors if any
$old_input = \Brenzley\Core\Session::getFlash('old_input') ?? [];
$errors = \Brenzley\Core\Session::getFlash('errors') ?? [];
?>

<div class="ai-editor-container">
    <!-- Header with controls -->
    <header class="ai-editor-header">
        <!-- Mobile hamburger menu button -->
        <button class="ai-header-mobile-menu" id="ai-header-mobile-menu" aria-label="Toggle menu">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
        </button>

        <div class="ai-editor-header-left">
            <button id="ai-save-draft" class="admin-btn admin-btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                            <polyline points="7 3 7 8 15 8"></polyline>
                        </svg>
                        <span class="ai-btn-text">Save Draft</span>
                    </button>
                </div>
                <div class="ai-editor-header-center">
                    <div class="ai-save-status" id="ai-save-status">Last saved: Never</div>
                </div>
                <div class="ai-editor-header-right">
                    <button id="ai-preview" class="admin-btn admin-btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <span class="ai-btn-text">Preview</span>
                    </button>
                    <button id="ai-publish" class="admin-btn admin-btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 2v16h-20v-16h3v10h14v-10h3z"></path>
                            <path d="M7 16l5 6 5-6"></path>
                        </svg>
                        <span class="ai-btn-text">Publish</span>
                    </button>
                </div>

                <!-- Mobile dropdown menu -->
                <div class="ai-header-mobile-dropdown" id="ai-header-mobile-dropdown">
                    <div class="ai-header-mobile-actions">
                        <button id="ai-save-draft-mobile" class="admin-btn admin-btn-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                <polyline points="7 3 7 8 15 8"></polyline>
                            </svg>
                            Save Draft
                        </button>
                        <button id="ai-publish-mobile" class="admin-btn admin-btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 2v16h-20v-16h3v10h14v-10h3z"></path>
                                <path d="M7 16l5 6 5-6"></path>
                            </svg>
                            Publish
                        </button>
                        <button id="ai-preview-mobile" class="admin-btn admin-btn-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            Preview
                        </button>
                    </div>
                    <div class="ai-save-status" id="ai-save-status-mobile">Last saved: Never</div>
                </div>
            </header>

            <!-- Main content area -->
            <div class="ai-editor-main">
                <!-- Article Creation Process -->
                <div class="ai-creation-process">
                    <!-- Step Indicator -->
                    <div class="ai-process-steps">
                        <div class="ai-process-step active" data-step="idea">
                            <div class="ai-step-number">1</div>
                            <div class="ai-step-label">Topic Ideas</div>
                        </div>
                        <div class="ai-process-step" data-step="outline">
                            <div class="ai-step-number">2</div>
                            <div class="ai-step-label">Article Outline</div>
                        </div>
                        <div class="ai-process-step" data-step="content">
                            <div class="ai-step-number">3</div>
                            <div class="ai-step-label">Full Content</div>
                        </div>
                        <div class="ai-process-step" data-step="seo">
                            <div class="ai-step-number">4</div>
                            <div class="ai-step-label">SEO Optimization</div>
                        </div>
                    </div>

                    <!-- Step Content -->
                    <div class="ai-process-content">
                        <!-- Step 1: Topic Ideas -->
                        <div class="ai-process-panel active" id="ai-step-idea">
                            <div class="ai-panel-header">
                                <h2 class="ai-panel-title">Generate Article Ideas</h2>
                                <p class="ai-panel-description">Let AI suggest article ideas based on your target audience and goals</p>
                            </div>

                            <div class="ai-panel-form">
                                <div class="ai-form-group">
                                    <label for="idea-topic" class="ai-form-label">Topic Area</label>
                                    <input type="text" id="idea-topic" class="ai-form-input" placeholder="e.g., Digital Marketing, Health & Wellness, Technology">
                                </div>

                                <div class="ai-form-group">
                                    <label for="idea-audience" class="ai-form-label">Target Audience</label>
                                    <input type="text" id="idea-audience" class="ai-form-input" placeholder="e.g., Small Business Owners, Fitness Enthusiasts, Tech Professionals">
                                </div>

                                <div class="ai-form-group">
                                    <label for="idea-goal" class="ai-form-label">Content Goal</label>
                                    <select id="idea-goal" class="ai-form-select">
                                        <option value="educate">Educate & Inform</option>
                                        <option value="solve">Solve a Problem</option>
                                        <option value="inspire">Inspire & Motivate</option>
                                        <option value="entertain">Entertain</option>
                                        <option value="convert">Drive Conversions</option>
                                    </select>
                                </div>

                                <button id="ai-generate-ideas" class="ai-btn ai-btn-primary ai-btn-full">
                                    Generate Ideas
                                </button>
                            </div>

                            <div class="ai-ideas-container" id="ai-ideas-container">
                                <!-- Ideas will be populated here -->
                            </div>
                        </div>

                        <!-- Step 2: Article Outline -->
                        <div class="ai-process-panel" id="ai-step-outline">
                            <div class="ai-panel-header">
                                <h2 class="ai-panel-title">Create Article Outline</h2>
                                <p class="ai-panel-description">Generate a structured outline for your article</p>
                            </div>

                            <div class="ai-selected-idea" id="ai-selected-idea">
                                <!-- Selected idea will be displayed here -->
                            </div>

                            <!-- Outline Customization Options -->
                            <div class="ai-outline-options">
                                <div class="ai-outline-options-header">
                                    <h3 class="ai-section-heading">Outline Options</h3>
                                    <button id="ai-toggle-outline-options" class="ai-toggle-button" title="Toggle options">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </button>
                                </div>

                                <div class="ai-outline-options-content" id="ai-outline-options-content">
                                    <div class="ai-options-grid">
                                        <div class="ai-option-group">
                                            <label class="ai-form-label">Outline Depth</label>
                                            <select id="ai-outline-depth" class="ai-form-select">
                                                <option value="h2">H2 Only (Basic)</option>
                                                <option value="h2-h3" selected>H2 + H3 (Standard)</option>
                                                <option value="h2-h3-h4">H2 + H3 + H4 (Detailed)</option>
                                            </select>
                                        </div>

                                        <div class="ai-option-group">
                                            <label class="ai-form-label">Outline Style</label>
                                            <select id="ai-outline-style" class="ai-form-select">
                                                <option value="descriptive" selected>Descriptive</option>
                                                <option value="question">Question-Based</option>
                                                <option value="howto">How-To Steps</option>
                                                <option value="listicle">Listicle</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Outline Visualization Tabs -->
                            <div class="ai-outline-tabs">
                                <button class="ai-outline-tab active" data-tab="list">List View</button>
                                <button class="ai-outline-tab" data-tab="visual">Visual Map</button>
                                <button class="ai-outline-tab" data-tab="toc">Table of Contents</button>
                            </div>

                            <!-- Outline Container (List View) -->
                            <div class="ai-outline-view active" id="ai-outline-list-view">
                                <div class="ai-outline-container" id="ai-outline-container">
                                    <!-- Outline will be populated here -->
                                </div>
                            </div>

                            <!-- Outline Container (Visual Map) -->
                            <div class="ai-outline-view" id="ai-outline-visual-view">
                                <div class="ai-outline-map-container" id="ai-outline-map-container">
                                    <!-- Visual map will be rendered here -->
                                </div>
                            </div>

                            <!-- Outline Container (Table of Contents) -->
                            <div class="ai-outline-view" id="ai-outline-toc-view">
                                <div class="ai-toc-options">
                                    <div class="ai-form-group">
                                        <label class="ai-form-label">TOC Style</label>
                                        <select id="ai-toc-style" class="ai-form-select">
                                            <option value="numbered">Numbered</option>
                                            <option value="bullets">Bullet Points</option>
                                            <option value="links">Clickable Links</option>
                                            <option value="collapsed">Collapsible</option>
                                        </select>
                                    </div>
                                    <div class="ai-form-group">
                                        <label class="ai-form-label">Include</label>
                                        <div class="ai-checkbox-group">
                                            <label class="ai-checkbox-label">
                                                <input type="checkbox" id="ai-toc-include-h2" checked> H2 Headings
                                            </label>
                                            <label class="ai-checkbox-label">
                                                <input type="checkbox" id="ai-toc-include-h3" checked> H3 Headings
                                            </label>
                                            <label class="ai-checkbox-label">
                                                <input type="checkbox" id="ai-toc-include-h4"> H4 Headings
                                            </label>
                                        </div>
                                    </div>
                                    <button id="ai-generate-toc" class="ai-btn ai-btn-secondary ai-btn-sm">
                                        Generate TOC
                                    </button>
                                </div>

                                <div class="ai-toc-preview-container">
                                    <h3 class="ai-section-heading">Table of Contents Preview</h3>
                                    <div class="ai-toc-preview" id="ai-toc-preview">
                                        <!-- TOC preview will be rendered here -->
                                    </div>
                                </div>

                                <div class="ai-toc-code-container">
                                    <h3 class="ai-section-heading">TOC Code</h3>
                                    <div class="ai-code-block-container">
                                        <pre class="ai-code-block" id="ai-toc-code"><!-- TOC code will be rendered here --></pre>
                                        <button id="ai-copy-toc-code" class="ai-copy-code-btn" title="Copy to clipboard">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="ai-panel-actions">
                                <button id="ai-regenerate-outline" class="ai-btn ai-btn-secondary">
                                    Regenerate Outline
                                </button>
                                <button id="ai-approve-outline" class="ai-btn ai-btn-primary">
                                    Approve & Continue
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Full Content -->
                        <div class="ai-process-panel" id="ai-step-content">
                            <div class="ai-panel-header">
                                <h2 class="ai-panel-title">Generate Full Article</h2>
                                <p class="ai-panel-description">AI will generate the complete article, including text and image placements, based on your approved outline.</p>
                            </div>

                            <div class="ai-title-section">
                                <label for="post-title" class="ai-form-label">Article Title</label>
                                <input type="text" id="post-title" name="title" class="ai-title-input" placeholder="Enter or confirm post title..." value="<?= htmlspecialchars($old_input['title'] ?? '') ?>">
                                <?php if (isset($errors['title'])): ?>
                                    <div class="ai-form-error"><?= htmlspecialchars($errors['title']) ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Content Generation Options (Optional - can be added later if needed) -->
                            <!--
                            <div class="ai-content-options">
                                <h3 class="ai-section-heading">Content Options</h3>
                                <div class="ai-form-group">
                                    <label for="content-tone" class="ai-form-label">Tone</label>
                                    <select id="content-tone" class="ai-form-select">
                                        <option value="professional" selected>Professional</option>
                                        <option value="casual">Casual</option>
                                        <option value="formal">Formal</option>
                                        <option value="witty">Witty</option>
                                        <option value="informative">Informative</option>
                                    </select>
                                </div>
                                <div class="ai-form-group">
                                    <label class="ai-checkbox-label">
                                        <input type="checkbox" id="content-include-images" checked> Include Image Placements
                                    </label>
                                </div>
                            </div>
                             -->

                            <div class="ai-content-editor-wrapper">
                                <div class="ai-content-area" id="ai-content-area" contenteditable="true" data-placeholder="AI will generate content here. You can also edit it directly.">
                                    <!-- AI generated content with image placeholders will appear here -->
                                </div>
                                
                                <!-- Floating Selection Toolbar (hidden by default) -->
                                <div class="ai-selection-toolbar" id="ai-selection-toolbar">
                                    <button class="ai-format-btn" data-format="bold" title="Bold (Ctrl+B)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                            <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                        </svg>
                                    </button>
                                    <button class="ai-format-btn" data-format="italic" title="Italic (Ctrl+I)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="19" y1="4" x2="10" y2="4"></line>
                                            <line x1="14" y1="20" x2="5" y2="20"></line>
                                            <line x1="15" y1="4" x2="9" y2="20"></line>
                                        </svg>
                                    </button>
                                    <button class="ai-format-btn" data-format="underline" title="Underline (Ctrl+U)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
                                            <line x1="4" y1="21" x2="20" y2="21"></line>
                                        </svg>
                                    </button>
                                    <div class="ai-toolbar-separator"></div>
                                    <div class="ai-heading-dropdown-container">
                                        <button class="ai-format-btn ai-heading-btn" data-format="heading" title="Select Heading">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M6 12h12"></path>
                                                <path d="M6 20V4"></path>
                                                <path d="M18 20V4"></path>
                                            </svg>
                                            <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="ai-dropdown-arrow">
                                                <polyline points="6 9 12 15 18 9"></polyline>
                                            </svg>
                                        </button>
                                        <div class="ai-heading-dropdown">
                                            <div class="ai-heading-option" data-heading="p">Paragraph</div>
                                            <div class="ai-heading-option" data-heading="h1">Heading 1</div>
                                            <div class="ai-heading-option" data-heading="h2">Heading 2</div>
                                            <div class="ai-heading-option" data-heading="h3">Heading 3</div>
                                            <div class="ai-heading-option" data-heading="h4">Heading 4</div>
                                            <div class="ai-heading-option" data-heading="h5">Heading 5</div>
                                            <div class="ai-heading-option" data-heading="h6">Heading 6</div>
                                        </div>
                                    </div>
                                    <button class="ai-format-btn" data-format="link" title="Insert Link (Ctrl+K)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                                        </svg>
                                    </button>
                                    <div class="ai-toolbar-separator"></div>
                                    <button class="ai-format-btn" data-format="list-ul" title="Bullet List">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="4" cy="6" r="2" fill="currentColor"></circle>
                                            <circle cx="4" cy="12" r="2" fill="currentColor"></circle>
                                            <circle cx="4" cy="18" r="2" fill="currentColor"></circle>
                                            <line x1="10" y1="6" x2="21" y2="6"></line>
                                            <line x1="10" y1="12" x2="21" y2="12"></line>
                                            <line x1="10" y1="18" x2="21" y2="18"></line>
                                        </svg>
                                    </button>
                                    <button class="ai-format-btn" data-format="list-ol" title="Numbered List">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                            <text x="3" y="8" font-family="Arial, sans-serif" font-size="7" font-weight="bold" fill="currentColor">1</text>
                                            <text x="3" y="16" font-family="Arial, sans-serif" font-size="7" font-weight="bold" fill="currentColor">2</text>
                                            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="1.5"></line>
                                            <line x1="8" y1="14" x2="21" y2="14" stroke="currentColor" stroke-width="1.5"></line>
                                        </svg>
                                    </button>
                                    <div class="ai-toolbar-separator"></div>
                                    <button class="ai-format-btn" data-format="table" title="Insert Table">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                <!-- Right-click Context Menu (hidden by default) -->
                                <div class="ai-context-menu" id="ai-context-menu">
                                    <div class="ai-context-menu-item" data-format="bold">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                            <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                        </svg>
                                        <span>Bold</span>
                                        <span class="ai-context-shortcut">Ctrl+B</span>
                                    </div>
                                    <div class="ai-context-menu-item" data-format="italic">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="19" y1="4" x2="10" y2="4"></line>
                                            <line x1="14" y1="20" x2="5" y2="20"></line>
                                            <line x1="15" y1="4" x2="9" y2="20"></line>
                                        </svg>
                                        <span>Italic</span>
                                        <span class="ai-context-shortcut">Ctrl+I</span>
                                    </div>
                                    <div class="ai-context-menu-item" data-format="underline">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
                                            <line x1="4" y1="21" x2="20" y2="21"></line>
                                        </svg>
                                        <span>Underline</span>
                                        <span class="ai-context-shortcut">Ctrl+U</span>
                                    </div>
                                    <div class="ai-context-menu-separator"></div>
                                    <div class="ai-context-menu-item ai-heading-submenu-trigger">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                            <path d="M6 12h12"></path>
                                            <path d="M6 20V4"></path>
                                            <path d="M18 20V4"></path>
                                        </svg>
                                        <span>Headings</span>
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="ai-submenu-arrow">
                                            <polyline points="9 18 15 12 9 6"></polyline>
                                        </svg>
                                        <div class="ai-context-submenu">
                                            <div class="ai-context-menu-item" data-format="p">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                                    <path d="M3 7h18"></path>
                                                    <path d="M3 12h18"></path>
                                                    <path d="M3 17h12"></path>
                                                </svg>
                                                <span>Paragraph</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h1">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M16 10L19 9L19 19M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 1</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h2">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M15 12.5V12C15 10.3431 16.3431 9 18 9H18.1716C19.7337 9 20.9996 10.2665 20.9996 11.8286C20.9996 12.5788 20.702 13.2982 20.1716 13.8286L15 19.0002L21 19M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 2</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h3">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M15 9H21L17 13H18C19.6569 13 21 14.3431 21 16C21 17.6569 19.6569 19 18 19C17.3793 19 16.7738 18.8077 16.2671 18.4492C15.7604 18.0907 15.3775 17.5838 15.1709 16.9985M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 3</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h4">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M18 9L15.5 17H20M20 17H21M20 17V14M20 17V19M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 4</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h5">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M21 9H17L15.75 14.0158C15.8285 13.9268 15.912 13.8429 16 13.7642C16.3509 13.4504 16.7731 13.2209 17.2346 13.0991C17.9263 12.9166 18.6611 12.9876 19.3053 13.2987C19.9495 13.6099 20.4608 14.1414 20.7479 14.7967C21.035 15.452 21.0788 16.188 20.8707 16.8725C20.6627 17.557 20.2165 18.1447 19.6133 18.5295C19.0101 18.9142 18.2895 19.0704 17.5811 18.9704C16.8726 18.8705 16.2232 18.521 15.75 17.9844M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 5</span>
                                            </div>
                                            <div class="ai-context-menu-item" data-format="h6">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <path d="M15.4024 14.5249C14.574 15.9516 15.0656 17.7759 16.5005 18.5997C17.9354 19.4234 19.7701 18.9346 20.5986 17.5078C21.427 16.0811 20.9352 14.2571 19.5003 13.4334C18.0655 12.6097 16.2309 13.0982 15.4024 14.5249ZM15.4024 14.5249L18.9998 8M3 5V12M3 12V19M3 12H11M11 5V12M11 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <span>Heading 6</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ai-context-menu-item" data-format="link">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                                        </svg>
                                        <span>Insert Link</span>
                                        <span class="ai-context-shortcut">Ctrl+K</span>
                                    </div>
                                    <div class="ai-context-menu-separator"></div>
                                    <div class="ai-context-menu-item" data-format="list-ul">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="4" cy="6" r="2" fill="currentColor"></circle>
                                            <circle cx="4" cy="12" r="2" fill="currentColor"></circle>
                                            <circle cx="4" cy="18" r="2" fill="currentColor"></circle>
                                            <line x1="10" y1="6" x2="21" y2="6"></line>
                                            <line x1="10" y1="12" x2="21" y2="12"></line>
                                            <line x1="10" y1="18" x2="21" y2="18"></line>
                                        </svg>
                                        <span>Bullet List</span>
                                    </div>
                                    <div class="ai-context-menu-item" data-format="list-ol">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <text x="3" y="8" font-family="Arial, sans-serif" font-size="7" font-weight="bold" fill="currentColor">1</text>
                                            <text x="3" y="16" font-family="Arial, sans-serif" font-size="7" font-weight="bold" fill="currentColor">2</text>
                                            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="1.5"></line>
                                            <line x1="8" y1="14" x2="21" y2="14" stroke="currentColor" stroke-width="1.5"></line>
                                        </svg>
                                        <span>Numbered List</span>
                                    </div>
                                    <div class="ai-context-menu-separator"></div>
                                    <div class="ai-context-menu-item" data-format="table">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>
                                        </svg>
                                        <span>Insert Table</span>
                                    </div>
                                </div>
                                
                                <!-- Table Toolbar (hidden by default) -->
                                <div class="ai-table-toolbar" id="ai-table-toolbar">
                                    <button class="ai-table-btn" data-action="add-row-above" data-tooltip="Add Row Above">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 12h18m-9-9v18"/>
                                            <path d="M3 7h18"/>
                                        </svg>
                                    </button>
                                    <button class="ai-table-btn" data-action="add-row-below" data-tooltip="Add Row Below">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 12h18m-9-9v18"/>
                                            <path d="M3 17h18"/>
                                        </svg>
                                    </button>
                                    <div class="ai-table-separator"></div>
                                    <button class="ai-table-btn" data-action="add-col-left" data-tooltip="Add Column Left">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M12 3v18m-9-9h18"/>
                                            <path d="M7 3v18"/>
                                        </svg>
                                    </button>
                                    <button class="ai-table-btn" data-action="add-col-right" data-tooltip="Add Column Right">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M12 3v18m-9-9h18"/>
                                            <path d="M17 3v18"/>
                                        </svg>
                                    </button>
                                    <div class="ai-table-separator"></div>
                                    <button class="ai-table-btn" data-action="delete-row" data-tooltip="Delete Current Row">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 6h18"/>
                                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                                            <path d="M10 11v6"/>
                                            <path d="M14 11v6"/>
                                        </svg>
                                    </button>
                                    <button class="ai-table-btn" data-action="delete-col" data-tooltip="Delete Current Column">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 6h18"/>
                                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                                            <path d="M10 11v6"/>
                                            <path d="M14 11v6"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Table Creation Dialog (hidden by default) -->
                                <div class="ai-table-create-dialog" id="ai-table-create-dialog">
                                    <div class="ai-table-create-content">
                                        <div class="ai-table-create-header">
                                            <h3>Create Table</h3>
                                            <p>Specify the number of rows and columns for your table.</p>
                                        </div>
                                        <div class="ai-table-create-form">
                                            <div class="ai-table-size-inputs">
                                                <div class="ai-form-group">
                                                    <label for="ai-table-rows" class="ai-form-label">Rows</label>
                                                    <input type="number" id="ai-table-rows" class="ai-form-input" min="1" max="20" value="3">
                                                </div>
                                                <div class="ai-form-group">
                                                    <label for="ai-table-cols" class="ai-form-label">Columns</label>
                                                    <input type="number" id="ai-table-cols" class="ai-form-input" min="1" max="10" value="3">
                                                </div>
                                            </div>
                                            <div class="ai-form-group">
                                                <label class="ai-checkbox-label">
                                                    <input type="checkbox" id="ai-table-header" checked>
                                                    Include header row
                                                </label>
                                            </div>
                                        </div>
                                        <div class="ai-table-create-actions">
                                            <button type="button" class="ai-btn ai-btn-secondary" id="ai-table-cancel">Cancel</button>
                                            <button type="button" class="ai-btn ai-btn-primary" id="ai-table-create">Create Table</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Custom Link Dialog (hidden by default) -->
                                <div class="ai-link-dialog" id="ai-link-dialog">
                                    <div class="ai-link-dialog-overlay"></div>
                                    <div class="ai-link-dialog-content">
                                        <div class="ai-link-dialog-header">
                                            <h3>Insert Link</h3>
                                            <button class="ai-link-dialog-close" id="ai-link-dialog-close">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="ai-link-dialog-body">
                                            <div class="ai-link-form-group">
                                                <label for="ai-link-url" class="ai-link-label">URL</label>
                                                <input type="url" id="ai-link-url" class="ai-link-input" placeholder="https://example.com" required>
                                            </div>
                                            <div class="ai-link-form-group">
                                                <label for="ai-link-text" class="ai-link-label">Link Text</label>
                                                <input type="text" id="ai-link-text" class="ai-link-input" placeholder="Enter link text">
                                            </div>
                                            <div class="ai-link-form-group">
                                                <label for="ai-link-title" class="ai-link-label">Title (optional)</label>
                                                <input type="text" id="ai-link-title" class="ai-link-input" placeholder="Tooltip text">
                                            </div>
                                            <div class="ai-link-options">
                                                <label class="ai-link-checkbox-label">
                                                    <input type="checkbox" id="ai-link-new-tab" class="ai-link-checkbox">
                                                    <span class="ai-link-checkmark"></span>
                                                    Open in new tab
                                                </label>
                                                <label class="ai-link-checkbox-label">
                                                    <input type="checkbox" id="ai-link-nofollow" class="ai-link-checkbox">
                                                    <span class="ai-link-checkmark"></span>
                                                    Add rel="nofollow"
                                                </label>
                                            </div>
                                        </div>
                                        <div class="ai-link-dialog-footer">
                                            <button type="button" class="ai-link-btn ai-link-btn-secondary" id="ai-link-cancel">Cancel</button>
                                            <button type="button" class="ai-link-btn ai-link-btn-primary" id="ai-link-insert">Insert Link</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ai-panel-actions">
                                <button id="ai-generate-initial-content" class="ai-btn ai-btn-primary">Generate Initial Content</button>
                                <button id="ai-regenerate-content" class="ai-btn ai-btn-secondary" style="display:none;">Regenerate Content</button>
                            </div>
                        </div>

                        <!-- Step 4: SEO Optimization -->
                        <div class="ai-process-panel" id="ai-step-seo">
                            <div class="ai-panel-header">
                                <h2 class="ai-panel-title">SEO Optimization</h2>
                                <p class="ai-panel-description">SEO optimization functionality is currently unavailable</p>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- Closing ai-creation-process -->
            </div> <!-- Closing ai-editor-main -->

    <!-- AI Assistant Chat - Moved outside of ai-editor-main for fixed positioning -->
    <div class="ai-assistant-chat" id="ai-assistant-chat-panel">
        <div class="ai-chat-header">
            <h3 class="ai-chat-title">AI Assistant</h3>
            <button id="ai-chat-close-btn" class="ai-chat-toggle" title="Close chat"> {/* Changed ID and title */}
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <div class="ai-chat-messages" id="ai-chat-messages">
            <!-- Chat messages will be populated here -->
            <div class="ai-chat-message ai-message-assistant">
                <div class="ai-message-avatar">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 16v-4"></path>
                        <path d="M12 8h.01"></path>
                    </svg>
                </div>
                <div class="ai-message-content">
                    <p>Hello! I'm your AI writing assistant. I'll help you create a high-quality article step by step. Let's start by generating some topic ideas. Please fill in the form above and click "Generate Ideas".</p>
                </div>
            </div>
        </div>

        <div class="ai-chat-input">
            <input type="text" id="ai-chat-input" class="ai-chat-input-field" placeholder="Ask me anything...">
            <button id="ai-chat-send" class="ai-chat-send-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
            </button>
        </div>
    </div>

    <!-- Chat backdrop for mobile overlay -->
    <div class="ai-chat-backdrop" id="ai-chat-backdrop"></div>

    <!-- Floating Action Button for Chat -->
    <button id="ai-chat-fab" class="ai-chat-fab" title="Open AI Assistant">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            <line x1="12" y1="12" x2="12" y2="12.01"></line> <line x1="8" y1="12" x2="8" y2="12.01"></line> <line x1="16" y1="12" x2="16" y2="12.01"></line>
        </svg>
    </button>
</div> <!-- Closing ai-editor-container -->

    <!-- Hidden form for submission -->
    <form id="post-form" action="/admin/posts/store" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?= \Brenzley\Core\Session::getCsrfToken() ?>">
        <input type="hidden" name="title" id="hidden-title">
        <input type="hidden" name="content" id="hidden-content">
        <input type="hidden" name="meta_title" id="hidden-meta-title">
        <input type="hidden" name="meta_description" id="hidden-meta-description">
        <input type="hidden" name="focus_keywords" id="hidden-focus-keywords">
        <input type="hidden" name="status" id="hidden-status" value="draft">
        <input type="hidden" name="category_id" id="hidden-category-id" value="1">
        <input type="hidden" name="featured_image_id" id="hidden-featured-image">
    </form>

<!-- Include the Featured Image Suggester CSS and JavaScript -->
<link rel="stylesheet" href="/assets/css/components/featured-image-suggester.css">
<script src="/assets/js/components/featured-image-suggester.js" defer></script>

<!-- Include the Media Library Dialog JavaScript (CSS is included in admin-ai-editor.css) -->
<script src="/assets/js/components/media-library-dialog.js" defer></script>

<!-- Include the Prompt Editor Dialog CSS and JavaScript -->
<link rel="stylesheet" href="/assets/css/components/prompt-editor-dialog.css">
<script src="/assets/js/components/prompt-editor-dialog.js" defer></script>

<!-- Include the AI Editor JavaScript -->
<script src="/assets/js/pages/admin-ai-editor.js" type="module" defer></script>

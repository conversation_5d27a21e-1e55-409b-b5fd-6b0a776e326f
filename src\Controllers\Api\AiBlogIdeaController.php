<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use <PERSON><PERSON><PERSON>y\Core\Session;
use B<PERSON>zley\Services\ApiProviders\ApiService;
use B<PERSON>zley\Services\ApiProviders\StreamingApiProviderInterface;
use B<PERSON>zley\Services\Prompts\PromptManager;

/**
 * Controller for AI blog idea generation
 */
class AiBlogIdeaController extends AiContentController
{
    /**
     * Generate a blog idea
     */
    public function generate(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['topic']) || empty($data['topic'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Topic is required'], 400);
            return;
        }

        $topic = $data['topic'];
        $parameters = $data['parameters'] ?? [];
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;
        $streaming = isset($data['streaming']) ? (bool)$data['streaming'] : false;

        // If streaming is requested, handle it differently
        if ($streaming) {
            $this->streamBlogIdea($topic, $parameters, $sessionId);
            return;
        }

        // Create API service
        $apiService = new ApiService();

        // Generate blog idea
        $result = $apiService->generateBlogIdea($topic, $parameters);

        if (!$result['success']) {
            $this->jsonResponse($result);
            return;
        }

        // Get active provider info
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        // Ensure we have a model value
        if (empty($activeProvider['selected_model'])) {
            $activeProvider['selected_model'] = 'default_model';
        }

        // Save the generated idea to the database
        $ideaData = [
            'user_id' => Session::get('user_id'),
            'topic' => $topic,
            'title' => $result['idea']['title'] ?? '',
            'description' => $result['idea']['description'] ?? '',
            'target_audience' => $result['idea']['target_audience'] ?? '',
            'key_points' => $result['idea']['key_points'] ?? [],
            'provider' => $activeProvider['provider'] ?? 'unknown',
            'model' => $activeProvider['selected_model'],
            'parameters' => $parameters
        ];

        $ideaId = $this->ideaModel->create($ideaData);

        if (!$ideaId) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to save blog idea',
                'idea' => $result['idea']
            ], 500);
            return;
        }

        // Update session if provided
        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);

            if ($session && $session['user_id'] === Session::get('user_id')) {
                $this->sessionModel->update($sessionId, ['idea_id' => $ideaId]);
            }
        }

        // Usage is now logged by the ApiService

        $this->jsonResponse([
            'success' => true,
            'idea' => $result['idea'],
            'idea_id' => $ideaId
        ]);
    }

    /**
     * Stream a blog idea generation
     *
     * @param string $topic The topic to generate an idea for
     * @param array $parameters Additional parameters for generation
     * @param int|null $sessionId Session ID if available
     * @return void
     */
    private function streamBlogIdea(string $topic, array $parameters = [], ?int $sessionId = null): void
    {
        // Set headers for streaming response
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Disable Nginx buffering

        // Disable output buffering
        if (ob_get_level()) ob_end_clean();

        // Send initial message
        send_sse_message('start', 'Starting blog idea generation...');

        // Get active provider
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        if (!$activeProvider) {
            send_sse_message('error', 'No active AI provider configured');
            exit;
        }

        // Create API provider instance
        $apiProvider = $this->createApiProvider($activeProvider);
        if (!$apiProvider) {
            send_sse_message('error', 'Failed to create API provider');
            exit;
        }

        // Get prompts from the centralized PromptManager
        $prompts = PromptManager::getBlogIdeaPrompts($topic, $parameters);

        // Add system prompt to options for the provider
        $options = $parameters;
        $options['system_prompt'] = $prompts['system'];

        // Collected content
        $collectedContent = '';

        try {
            // Start streaming
            $apiProvider->streamCompletion($prompts['user'], function($chunk) use (&$collectedContent) {
                $collectedContent .= $chunk;
                send_sse_message('chunk', $chunk);
            }, $options);

            // Parse the collected content into individual ideas
            $parsedIdeas = $this->parseIdeasFromResponse($collectedContent);

            // Ensure we have a model value
            if (empty($activeProvider['selected_model'])) {
                $activeProvider['selected_model'] = 'default_model';
            }

            $savedIdeas = [];
            foreach ($parsedIdeas as $idea) {
                try {
                    $ideaData = [
                        'user_id' => Session::get('user_id'),
                        'topic' => $topic,
                        'title' => mb_substr($idea['title'], 0, 250),
                        'description' => mb_substr($idea['description'], 0, 1000),
                        'target_audience' => mb_substr($idea['target_audience'], 0, 250),
                        'key_points' => $idea['key_points'] ?? [],
                        'provider' => $activeProvider['provider'] ?? 'unknown',
                        'model' => $activeProvider['selected_model'],
                        'parameters' => $parameters
                    ];

                    $ideaId = $this->ideaModel->create($ideaData);
                    if ($ideaId) {
                        $idea['db_id'] = $ideaId;
                        $savedIdeas[] = $idea;
                    }
                } catch (\Throwable $e) {
                    error_log('Error saving individual blog idea to database: ' . $e->getMessage());
                    // Continue to the next idea
                }
            }

            // Send completion message with all the successfully saved ideas
            send_sse_message('end', [
                'message' => 'Blog idea generation completed',
                'ideas' => $savedIdeas
            ]);
        } catch (\Throwable $e) {
            // Send error message
            send_sse_message('error', 'Error: ' . $e->getMessage());
        }

        exit;
    }

    /**
     * Parses the raw AI response into an array of individual idea arrays.
     *
     * @param string $response The raw text response from the AI.
     * @return array An array of ideas, where each idea is an associative array.
     */
    private function parseIdeasFromResponse(string $response): array
    {
        $ideas = [];
        // Split the entire response by the "IDEA #[number]" marker.
        $ideaBlocks = preg_split('/IDEA\s+#\d+/i', $response, -1, PREG_SPLIT_NO_EMPTY);

        foreach ($ideaBlocks as $index => $block) {
            $idea = [
                'client_id' => 'idea-' . ($index + 1),
                'title' => '',
                'description' => '',
                'target_audience' => '',
                'key_points' => []
            ];

            // Extract Title
            if (preg_match('/Title:\s*(.*)/i', $block, $matches)) {
                $idea['title'] = trim($matches[1]);
            }

            // Extract Description
            if (preg_match('/Description:\s*(.*?)(?=Target Audience:|Key Points:|$)/is', $block, $matches)) {
                $idea['description'] = trim($matches[1]);
            }

            // Extract Target Audience
            if (preg_match('/Target Audience:\s*(.*?)(?=Key Points:|$)/is', $block, $matches)) {
                $idea['target_audience'] = trim($matches[1]);
            }

            // Extract Key Points
            if (preg_match('/Key Points:\s*([\s\S]*)/is', $block, $matches)) {
                $pointsBlock = $matches[1];
                $points = preg_split('/^\s*[-*]\s*/m', $pointsBlock, -1, PREG_SPLIT_NO_EMPTY);
                $idea['key_points'] = array_map('trim', $points);
            }

            if (!empty($idea['title'])) {
                $ideas[] = $idea;
            }
        }

        return $ideas;
    }

    /**
     * Create an API provider instance from provider settings
     *
     * @param array $provider Provider settings
     * @return \Brenzley\Services\ApiProviders\StreamingApiProviderInterface|null
     */
    private function createApiProvider(array $provider): ?\Brenzley\Services\ApiProviders\StreamingApiProviderInterface
    {
        // Get API key
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($provider['provider']) ?? '';
        if (empty($apiKey)) {
            return null;
        }

        // Get provider settings
        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $provider['selected_model'] ?? null,
            'context_window' => (int)($provider['context_window'] ?? 8096),
            'settings' => json_decode($provider['settings'] ?? '{}', true) ?? []
        ];

        // Create provider instance
        return \Brenzley\Services\ApiProviders\ApiProviderFactory::create($provider['provider'], $settings);
    }

    /**
     * Get a blog idea
     */
    public function get(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get idea ID from query parameters
        $ideaId = isset($_GET['id']) ? (int)$_GET['id'] : null;

        if (!$ideaId) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea ID is required'], 400);
            return;
        }

        // Get idea
        $idea = $this->ideaModel->findById($ideaId);

        if (!$idea) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea not found'], 404);
            return;
        }

        // Check if the idea belongs to the current user
        if ($idea['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to idea'], 403);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'idea' => $idea
        ]);
    }

    /**
     * List blog ideas
     */
    public function list(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 10;
        $offset = ($page - 1) * $limit;

        // Get ideas
        $ideas = $this->ideaModel->findByUserId(Session::get('user_id'), $limit, $offset);
        $total = $this->ideaModel->countByUserId(Session::get('user_id'));

        $this->jsonResponse([
            'success' => true,
            'ideas' => $ideas,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    }

    /**
     * Update a blog idea
     */
    public function update(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['id']) || empty($data['id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea ID is required'], 400);
            return;
        }

        $ideaId = (int)$data['id'];

        // Get idea
        $idea = $this->ideaModel->findById($ideaId);

        if (!$idea) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea not found'], 404);
            return;
        }

        // Check if the idea belongs to the current user
        if ($idea['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to idea'], 403);
            return;
        }

        // Update idea
        $updateData = [];

        if (isset($data['title'])) {
            $updateData['title'] = $data['title'];
        }

        if (isset($data['description'])) {
            $updateData['description'] = $data['description'];
        }

        if (isset($data['target_audience'])) {
            $updateData['target_audience'] = $data['target_audience'];
        }

        if (isset($data['key_points'])) {
            $updateData['key_points'] = $data['key_points'];
        }

        if (empty($updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'No data to update'], 400);
            return;
        }

        $success = $this->ideaModel->update($ideaId, $updateData);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update idea'], 500);
            return;
        }

        // Get updated idea
        $updatedIdea = $this->ideaModel->findById($ideaId);

        $this->jsonResponse([
            'success' => true,
            'message' => 'Idea updated successfully',
            'idea' => $updatedIdea
        ]);
    }

    /**
     * Stream a blog idea generation (GET endpoint)
     */
    public function stream(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            send_sse_message('error', 'Authentication required');
            exit;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            send_sse_message('error', 'Invalid request method');
            exit;
        }

        // Get topic from query parameters
        $topic = isset($_GET['topic']) ? trim($_GET['topic']) : '';

        if (empty($topic)) {
            send_sse_message('error', 'Topic is required');
            exit;
        }

        // Get additional parameters
        $parameters = [];
        if (isset($_GET['audience'])) {
            $parameters['audience'] = $_GET['audience'];
        }
        if (isset($_GET['goal'])) {
            $parameters['goal'] = $_GET['goal'];
        }

        // Get session ID if provided
        $sessionId = isset($_GET['session_id']) ? (int)$_GET['session_id'] : null;

        // Stream the blog idea
        $this->streamBlogIdea($topic, $parameters, $sessionId);
    }

    /**
     * Delete a blog idea
     */
    public function delete(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'DELETE' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get idea ID
        $data = json_decode(file_get_contents('php://input'), true);
        $ideaId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$ideaId) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea ID is required'], 400);
            return;
        }

        // Get idea
        $idea = $this->ideaModel->findById($ideaId);

        if (!$idea) {
            $this->jsonResponse(['success' => false, 'message' => 'Idea not found'], 404);
            return;
        }

        // Check if the idea belongs to the current user
        if ($idea['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to idea'], 403);
            return;
        }

        // Delete idea
        $success = $this->ideaModel->delete($ideaId);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete idea'], 500);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'message' => 'Idea deleted successfully'
        ]);
    }
}

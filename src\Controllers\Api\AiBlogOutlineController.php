<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use B<PERSON><PERSON>y\Core\Session;
use Brenzley\Services\ApiProviders\ApiService;
use Brenzley\Services\Prompts\PromptManager;

/**
 * Controller for AI blog outline generation
 */
class AiBlogOutlineController extends AiContentController
{
    /**
     * Generate a blog outline
     */
    public function generate(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['title']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Title is required'], 400);
            return;
        }

        if (!isset($data['description']) || empty($data['description'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Description is required'], 400);
            return;
        }

        $title = $data['title'];
        $description = $data['description'];
        $parameters = $data['parameters'] ?? [];
        $blogIdeaId = isset($data['blog_idea_id']) ? (int)$data['blog_idea_id'] : null;
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;

        // Create API service
        $apiService = new ApiService();

        // Generate blog outline
        $result = $apiService->generateBlogOutline($title, $description, $parameters);

        if (!$result['success']) {
            $this->jsonResponse($result);
            return;
        }

        // Get active provider info
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        // Ensure we have a model value
        if (empty($activeProvider['selected_model'])) {
            $activeProvider['selected_model'] = 'default_model';
        }

        // Save the generated outline to the database
        $outlineData = [
            'user_id' => Session::get('user_id'),
            'blog_idea_id' => $blogIdeaId,
            'title' => $title,
            'description' => $description,
            'outline' => $result['outline'],
            'provider' => $activeProvider['provider'] ?? 'unknown',
            'model' => $activeProvider['selected_model'],
            'parameters' => $parameters
        ];

        $outlineId = $this->outlineModel->create($outlineData);

        if (!$outlineId) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to save blog outline',
                'outline' => $result['outline']
            ], 500);
            return;
        }

        // Update session if provided
        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);

            if ($session && $session['user_id'] === Session::get('user_id')) {
                $this->sessionModel->update($sessionId, ['outline_id' => $outlineId]);
            }
        }

        // Usage is now logged by the ApiService

        $this->jsonResponse([
            'success' => true,
            'outline' => $result['outline'],
            'outline_id' => $outlineId
        ]);
    }

    /**
     * Get a blog outline
     */
    public function get(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get outline ID from query parameters
        $outlineId = isset($_GET['id']) ? (int)$_GET['id'] : null;

        if (!$outlineId) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline ID is required'], 400);
            return;
        }

        // Get outline
        $outline = $this->outlineModel->findById($outlineId);

        if (!$outline) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline not found'], 404);
            return;
        }

        // Check if the outline belongs to the current user
        if ($outline['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to outline'], 403);
            return;
        }

        // Get sections if available
        $sections = $this->sectionModel->findByOutlineId($outlineId);

        $this->jsonResponse([
            'success' => true,
            'outline' => $outline,
            'sections' => $sections
        ]);
    }

    /**
     * List blog outlines
     */
    public function list(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 10;
        $offset = ($page - 1) * $limit;

        // Get blog idea ID filter if provided
        $blogIdeaId = isset($_GET['blog_idea_id']) ? (int)$_GET['blog_idea_id'] : null;

        // Get outlines
        $outlines = [];
        $total = 0;

        if ($blogIdeaId) {
            // Check if the blog idea belongs to the current user
            $blogIdea = $this->ideaModel->findById($blogIdeaId);

            if (!$blogIdea || $blogIdea['user_id'] !== Session::get('user_id')) {
                $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to blog idea'], 403);
                return;
            }

            $outlines = $this->outlineModel->findByBlogIdeaId($blogIdeaId);
            $total = count($outlines);

            // Apply pagination manually
            $outlines = array_slice($outlines, $offset, $limit);
        } else {
            $outlines = $this->outlineModel->findByUserId(Session::get('user_id'), $limit, $offset);
            $total = $this->outlineModel->countByUserId(Session::get('user_id'));
        }

        $this->jsonResponse([
            'success' => true,
            'outlines' => $outlines,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    }

    /**
     * Stream a blog outline generation
     */
    public function stream(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            send_sse_message('error', 'Authentication required');
            exit;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            send_sse_message('error', 'Invalid request method');
            exit;
        }

        // Get parameters from query string
        $title = isset($_GET['title']) ? trim($_GET['title']) : '';
        $description = isset($_GET['description']) ? trim($_GET['description']) : '';

        if (empty($title)) {
            send_sse_message('error', 'Title is required');
            exit;
        }

        if (empty($description)) {
            send_sse_message('error', 'Description is required');
            exit;
        }

        // Get additional parameters
        $parameters = [];
        // Removed section_count parameter to give AI full freedom
        if (isset($_GET['outline_depth'])) {
            $parameters['outline_depth'] = $_GET['outline_depth'];
        }
        if (isset($_GET['outline_style'])) {
            $parameters['outline_style'] = $_GET['outline_style'];
        }
        if (isset($_GET['preset'])) {
            $parameters['preset'] = $_GET['preset'];
        }

        // Get blog idea ID if provided
        $blogIdeaId = isset($_GET['blog_idea_id']) ? (int)$_GET['blog_idea_id'] : null;

        // Get session ID if provided
        $sessionId = isset($_GET['session_id']) ? (int)$_GET['session_id'] : null;

        // Stream the blog outline
        $this->streamBlogOutline($title, $description, $parameters, $blogIdeaId, $sessionId);
    }

    /**
     * Stream a blog outline generation
     *
     * @param string $title The blog post title
     * @param string $description The blog post description
     * @param array $parameters Additional parameters for generation
     * @param int|null $blogIdeaId Blog idea ID if available
     * @param int|null $sessionId Session ID if available
     * @return void
     */
    private function streamBlogOutline(string $title, string $description, array $parameters = [], ?int $blogIdeaId = null, ?int $sessionId = null): void
    {
        // Set headers for streaming response
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Disable Nginx buffering

        // Disable output buffering
        if (ob_get_level()) ob_end_clean();

        // Send initial message
        send_sse_message('start', 'Starting blog outline generation...');

        // Get active provider
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        if (!$activeProvider) {
            send_sse_message('error', 'No active AI provider configured');
            exit;
        }

        // Create API provider instance
        $apiProvider = $this->createApiProvider($activeProvider);

        if (!$apiProvider) {
            send_sse_message('error', 'Failed to create API provider');
            exit;
        }

        // Get prompts from the centralized PromptManager
        $prompts = PromptManager::getBlogOutlinePrompts($title, $description, $parameters);

        // Add system prompt to options for the provider
        $options = $parameters;
        $options['system_prompt'] = $prompts['system'];

        // Collected content for parsing
        $collectedContent = '';

        try {
            // Start streaming
            $apiProvider->streamCompletion($prompts['user'], function($chunk) use (&$collectedContent) {
                $collectedContent .= $chunk;
                send_sse_message('chunk', $chunk);
            }, $options);

            // Ensure we have a model value
            if (empty($activeProvider['selected_model'])) {
                $activeProvider['selected_model'] = 'default_model';
            }

            // Save the generated outline to the database
            $outlineData = [
                'user_id' => Session::get('user_id'),
                'blog_idea_id' => $blogIdeaId,
                'title' => $title,
                'description' => $description,
                'outline' => $collectedContent,
                'provider' => $activeProvider['provider'] ?? 'unknown',
                'model' => $activeProvider['selected_model'],
                'parameters' => $parameters
            ];

            $outlineId = $this->outlineModel->create($outlineData);

            // Update session if provided
            if ($sessionId && $outlineId) {
                $session = $this->sessionModel->findById($sessionId);

                if ($session && $session['user_id'] === Session::get('user_id')) {
                    $this->sessionModel->update($sessionId, ['outline_id' => $outlineId]);
                }
            }

            // Send completion message
            send_sse_message('end', [
                'message' => 'Blog outline generation completed',
                'outline' => $collectedContent,
                'outline_id' => $outlineId ?? null
            ]);
        } catch (\Throwable $e) {
            // Send error message
            send_sse_message('error', 'Error: ' . $e->getMessage());
        }

        exit;
    }

    /**
     * Create an API provider instance from provider settings
     *
     * @param array $provider Provider settings
     * @return \Brenzley\Services\ApiProviders\StreamingApiProviderInterface|null
     */
    private function createApiProvider(array $provider): ?\Brenzley\Services\ApiProviders\StreamingApiProviderInterface
    {
        // Get API key
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($provider['provider']) ?? '';
        if (empty($apiKey)) {
            return null;
        }

        // Get provider settings
        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $provider['selected_model'] ?? null,
            'context_window' => (int)($provider['context_window'] ?? 8096),
            'settings' => json_decode($provider['settings'] ?? '{}', true) ?? []
        ];

        // Create provider instance using the factory
        return \Brenzley\Services\ApiProviders\ApiProviderFactory::create($provider['provider'], $settings);
    }

    /**
     * Update a blog outline
     */
    public function update(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['id']) || empty($data['id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline ID is required'], 400);
            return;
        }

        $outlineId = (int)$data['id'];

        // Get outline
        $outline = $this->outlineModel->findById($outlineId);

        if (!$outline) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline not found'], 404);
            return;
        }

        // Check if the outline belongs to the current user
        if ($outline['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to outline'], 403);
            return;
        }

        // Update outline
        $updateData = [];

        if (isset($data['title'])) {
            $updateData['title'] = $data['title'];
        }

        if (isset($data['description'])) {
            $updateData['description'] = $data['description'];
        }

        if (isset($data['outline'])) {
            $updateData['outline'] = $data['outline'];
        }

        if (empty($updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'No data to update'], 400);
            return;
        }

        $success = $this->outlineModel->update($outlineId, $updateData);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update outline'], 500);
            return;
        }

        // Get updated outline
        $updatedOutline = $this->outlineModel->findById($outlineId);

        $this->jsonResponse([
            'success' => true,
            'message' => 'Outline updated successfully',
            'outline' => $updatedOutline
        ]);
    }

    /**
     * Delete a blog outline
     */
    public function delete(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'DELETE' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get outline ID
        $data = json_decode(file_get_contents('php://input'), true);
        $outlineId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$outlineId) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline ID is required'], 400);
            return;
        }

        // Get outline
        $outline = $this->outlineModel->findById($outlineId);

        if (!$outline) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline not found'], 404);
            return;
        }

        // Check if the outline belongs to the current user
        if ($outline['user_id'] !== Session::get('user_id')) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to outline'], 403);
            return;
        }

        // Delete outline
        $success = $this->outlineModel->delete($outlineId);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete outline'], 500);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'message' => 'Outline deleted successfully'
        ]);
    }
}

<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use B<PERSON>zley\Core\Session;
use B<PERSON>zley\Services\ApiProviders\ApiService;
use B<PERSON>zley\Models\AiBlogPostModel;
use B<PERSON>zley\Services\ApiProviders\StreamingApiProviderInterface;
use Brenzley\Services\ApiProviders\ApiProviderFactory;
use Brenzley\Services\Prompts\PromptManager; // Added
use Brenzley\Models\AiBlogIdeaModel;     // Included for potential use by parent or future methods
use B<PERSON><PERSON>y\Models\AiBlogOutlineModel;  // Included for potential use by parent or future methods
use B<PERSON>zley\Models\AiContentSessionModel; // Included for potential use by parent or future methods
use Brenzley\Models\ApiSettingsModel;    // Included for potential use by parent or future methods
// use Brenzley\Models\AiSeoMetadataModel; // Assuming this might be used by parent or future methods

/**
 * Controller for AI blog post generation
 */
class AiBlogPostController extends AiContentController
{
    private AiBlogPostModel $aiBlogPostModel;
    // Other models like $this->ideaModel, $this->outlineModel, $this->sessionModel, 
    // $this->apiSettingsModel, $this->seoModel are expected to be initialized 
    // by the parent AiContentController's constructor.

    public function __construct()
    {
        parent::__construct(); // This should initialize models from AiContentController
        $this->aiBlogPostModel = new AiBlogPostModel();
    }

    public function streamFullContent(): void
    {
        if (!Session::isLoggedIn()) {
            $this->sendSseError('Authentication required', 401);
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendSseError('Invalid request method. This endpoint requires POST.', 405);
            return;
        }

        // Set headers for streaming response
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Disable Nginx buffering
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');

        // Disable output buffering completely
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Disable implicit flush and set up for immediate output
        ini_set('implicit_flush', '1');
        ini_set('output_buffering', '0');
        
        // If using FastCGI, finish the request to avoid buffering
        if (function_exists('fastcgi_finish_request')) {
            // We'll call this after headers but before streaming
            register_shutdown_function('fastcgi_finish_request');
        }

        $data = json_decode(file_get_contents('php://input'), true);

        $title = $data['title'] ?? '';
        $outline = $data['outline'] ?? [];
        $ideaPreset = $data['ideaPreset'] ?? 'professional';
        $blogIdeaId = !empty($data['blog_idea_id']) ? (int)$data['blog_idea_id'] : null;
        $outlineId = !empty($data['outline_id']) ? (int)$data['outline_id'] : null;
        $userId = Session::get('user_id');

        if (empty($title) || empty($outline) || !$userId) {
            $this->sendSseError('Missing required parameters (title, outline, or user session).');
            return;
        }
        if (!is_array($outline)) {
            $this->sendSseError('Invalid outline format. Expected JSON array.');
            return;
        }

        $prompts = PromptManager::getFullArticlePrompts($title, $outline, $ideaPreset);
        
        $fullGeneratedContent = '';
        $activeProviderSettings = $this->apiSettingsModel->getActiveProvider();

        if (!$activeProviderSettings || $activeProviderSettings['provider'] === 'none') {
            $this->sendSseError('No active AI provider configured.');
            return;
        }

        $apiProvider = $this->createApiProvider($activeProviderSettings);

        if (!$apiProvider) {
            $this->sendSseError('Failed to create API provider instance.');
            return;
        }

        try {
            $aiParameters = [
                'system_prompt' => $prompts['system']
            ];

            // Send start message
            echo "data: " . json_encode(['type' => 'start', 'message' => 'Starting content generation...']) . "\n\n";
            flush();

            // Call streamCompletion on the specific provider instance
            $apiProvider->streamCompletion($prompts['user'], function($chunk) use (&$fullGeneratedContent) {
                $fullGeneratedContent .= $chunk;
                if ($chunk !== null && $chunk !== '') {
                    // Send chunk in the format expected by frontend with immediate flush
                    echo "data: " . json_encode(['type' => 'chunk', 'content' => $chunk]) . "\n\n";
                    
                    // Force immediate output
                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                }
            }, $aiParameters);

            $postId = $this->aiBlogPostModel->create([
                'user_id' => $userId,
                'blog_idea_id' => $blogIdeaId,
                'outline_id' => $outlineId,
                'title' => $title,
                'content_html' => $fullGeneratedContent,
                'provider' => $activeProviderSettings['provider'] ?? 'unknown',
                'model' => $activeProviderSettings['selected_model'] ?? 'unknown',
                'parameters' => $aiParameters // Store parameters used for generation
            ]);

            if (!$postId) {
                error_log("Failed to save the AI generated blog post to the database. User ID: {$userId}, Title: {$title}");
                echo "data: " . json_encode(['type' => 'end', 'content' => ['full_content' => $fullGeneratedContent, 'post_id' => null, 'message' => 'Content generated but failed to save.']]) . "\n\n";
                flush();
                return;
            }

            echo "data: " . json_encode(['type' => 'end', 'content' => ['full_content' => $fullGeneratedContent, 'post_id' => $postId]]) . "\n\n";
            flush();

        } catch (\Throwable $e) {
            error_log("Error during content streaming API: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            // Determine error type and provide specific handling
            $errorType = 'unknown';
            $errorMessage = $e->getMessage();
            
            if (strpos($errorMessage, 'Base URL is required') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'OpenAI compatible provider requires a base URL to be configured.';
            } elseif (strpos($errorMessage, 'No model selected') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'No model has been selected for this provider.';
            } elseif (strpos($errorMessage, 'Connection') !== false || strpos($errorMessage, 'cURL') !== false) {
                $errorType = 'network';
                $errorMessage = 'Network connection error: ' . $errorMessage;
            } elseif (strpos($errorMessage, 'timeout') !== false) {
                $errorType = 'timeout';
                $errorMessage = 'Request timed out. The AI provider may be experiencing high load.';
            } elseif (strpos($errorMessage, 'Unauthorized') !== false || strpos($errorMessage, '401') !== false) {
                $errorType = 'authentication';
                $errorMessage = 'Authentication failed. Please check your API key.';
            } elseif (strpos($errorMessage, 'Rate limit') !== false || strpos($errorMessage, '429') !== false) {
                $errorType = 'rate_limit';
                $errorMessage = 'Rate limit exceeded. Please wait before making another request.';
            }

            // Send detailed error in SSE format
            echo "data: " . json_encode([
                'type' => 'error', 
                'message' => $errorMessage,
                'error_type' => $errorType,
                'provider' => $activeProviderSettings['provider'] ?? 'unknown'
            ]) . "\n\n";
            flush();
        } finally {
            if (ob_get_level() > 0) {
                ob_end_flush();
            }
            flush();
            exit;
        }
    }

    private function sendSseError(string $message, int $httpStatusCode = 500): void
    {
        if (!headers_sent() && $httpStatusCode !== 200) {
             http_response_code($httpStatusCode);
        }
        if (!headers_sent()) {
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
        }
        echo "data: " . json_encode(['type' => 'error', 'message' => $message]) . "\n\n";
        if (ob_get_level() > 0) {
            ob_flush();
        }
        flush();
        exit;
    }

    /**
     * Create an API provider instance from provider settings
     *
     * @param array $providerSettings Provider settings from the database
     * @return StreamingApiProviderInterface|null
     */
    private function createApiProvider(array $providerSettings): ?StreamingApiProviderInterface
    {
        if (empty($providerSettings['provider']) || $providerSettings['provider'] === 'none') {
            error_log('Attempted to create API provider with no provider specified.');
            return null;
        }

        // Get API key
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($providerSettings['provider']);
        if (empty($apiKey)) {
            error_log('Failed to get decrypted API key for provider: ' . $providerSettings['provider']);
            return null;
        }

        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $providerSettings['selected_model'] ?? null,
            'context_window' => (int)($providerSettings['context_window'] ?? 8096),
            'base_url' => $providerSettings['base_url'] ?? '', // Include base_url from database settings
            'settings' => json_decode($providerSettings['settings'] ?? '{}', true) ?? []
        ];
        
        try {
            $providerInstance = ApiProviderFactory::create($providerSettings['provider'], $settings);
            if ($providerInstance instanceof StreamingApiProviderInterface) {
                return $providerInstance;
            } else {
                error_log('API Provider created by factory does not implement StreamingApiProviderInterface: ' . $providerSettings['provider']);
                return null;
            }
        } catch (\Throwable $e) {
            error_log('Error creating API provider instance: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate a complete blog post (Non-streaming placeholder)
     */
    public function generate(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['title']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Title is required'], 400);
            return;
        }
        if (!isset($data['outline']) || empty($data['outline'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline is required'], 400);
            return;
        }

        $title = $data['title'];
        $outline = $data['outline']; 
        $parameters = $data['parameters'] ?? [];
        $blogIdeaId = $data['blog_idea_id'] ?? null;
        $blogOutlineId = isset($data['blog_outline_id']) ? (int)$data['blog_outline_id'] : null;
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;
        $userId = Session::getUserId();

        if ($blogOutlineId) {
            $outlineObjFromDb = $this->outlineModel->findById($blogOutlineId);
            if (!$outlineObjFromDb || $outlineObjFromDb['user_id'] !== $userId) {
                $this->jsonResponse(['success' => false, 'message' => 'Unauthorized or invalid outline ID'], 403);
                return;
            }
        }
        
        $apiService = new ApiService();
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        $prompts = PromptManager::getFullArticlePrompts($title, $outline, $parameters['ideaPreset'] ?? 'professional');
        $systemPrompt = $prompts['system'];
        $userPrompt = $prompts['user'];

        // This generateBlogPost method in ApiService would need to be non-streaming
        $result = $apiService->generateBlogPost($systemPrompt, $userPrompt, $parameters); 

        if (!$result['success'] || !isset($result['blog_post'])) {
            $this->jsonResponse($result ?: ['success' => false, 'message' => 'Failed to generate blog post content.']);
            return;
        }

        $postData = [
            'user_id' => $userId,
            'blog_idea_id' => $blogIdeaId, 
            'outline_id' => $blogOutlineId,
            'title' => $title,
            'content_html' => $result['blog_post'],
            'provider' => $activeProvider['provider'] ?? 'unknown',
            'model' => $activeProvider['selected_model'] ?? 'unknown',
            'parameters' => $parameters
        ];

        $postId = $this->aiBlogPostModel->create($postData);

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to save blog post', 'blog_post' => $result['blog_post']], 500);
            return;
        }

        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);
            if ($session && $session['user_id'] === $userId) {
                $this->sessionModel->update($sessionId, ['post_id' => $postId, 'status' => 'completed']);
            }
        }

        $this->jsonResponse(['success' => true, 'blog_post' => $result['blog_post'], 'post_id' => $postId]);
    }

    public function generateFromSections(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['blog_outline_id']) || empty($data['blog_outline_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Blog outline ID is required'], 400);
            return;
        }

        $blogOutlineId = (int)$data['blog_outline_id'];
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;
        $userId = Session::getUserId();

        $outline = $this->outlineModel->findById($blogOutlineId);
        if (!$outline || $outline['user_id'] !== $userId) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized or invalid blog outline'], 403);
            return;
        }

        // $this->sectionModel is not defined in this controller or parent AiContentController
        // Assuming it should be $this->aiBlogSectionModel if such a model exists, or this logic needs review
        // For now, commenting out the part that uses $this->sectionModel
        /*
        $sections = $this->sectionModel->findByOutlineId($blogOutlineId); 
        if (empty($sections)) {
            $this->jsonResponse(['success' => false, 'message' => 'No sections found for this outline'], 404);
            return;
        }
        usort($sections, function($a, $b) {
            return ($a['section_order'] ?? 0) <=> ($b['section_order'] ?? 0);
        });

        $contentHtml = ""; 
        foreach ($sections as $section) {
            $contentHtml .= "<h2>" . htmlspecialchars($section['title'] ?? 'Untitled Section') . "</h2>\n";
            $contentHtml .= "<div>" . ($section['content'] ?? '') . "</div>\n\n";
        }
        */
        // Placeholder content since sectionModel is not available here
        $contentHtml = "<p>Content generated from sections would appear here.</p>";
        
        $title = $outline['title'];
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        $postData = [
            'user_id' => $userId,
            'blog_idea_id' => $outline['blog_idea_id'] ?? null,
            'outline_id' => $blogOutlineId,
            'title' => $title,
            'content_html' => $contentHtml,
            'provider' => $activeProvider['provider'] ?? 'manual_sections',
            'model' => $activeProvider['model'] ?? 'from_sections',
            'parameters' => ['generated_from_sections' => true]
        ];

        $postId = $this->aiBlogPostModel->create($postData);

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to save blog post from sections', 'blog_post' => $contentHtml], 500);
            return;
        }

        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);
            if ($session && $session['user_id'] === $userId) {
                $this->sessionModel->update($sessionId, ['post_id' => $postId, 'status' => 'completed']);
            }
        }
        
        $this->jsonResponse(['success' => true, 'blog_post' => $contentHtml, 'post_id' => $postId]);
    }

    public function get(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $postId = isset($_GET['id']) ? (int)$_GET['id'] : null;
        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }

        $post = $this->aiBlogPostModel->findById($postId);

        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }
        
        // $seoMetadata = $this->seoModel->findByContent($postId, 'ai_blog_posts');
        $this->jsonResponse(['success' => true, 'post' => $post /*, 'seo_metadata' => $seoMetadata ?: null */ ]);
    }

    public function list(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 10;
        $offset = ($page - 1) * $limit;
        $userId = Session::getUserId();

        $posts = $this->aiBlogPostModel->findByUserId($userId, $limit, $offset);
        $total = $this->aiBlogPostModel->countByUserId($userId);

        $this->jsonResponse([
            'success' => true,
            'posts' => $posts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'total_pages' => $total > 0 ? ceil($total / $limit) : 0
            ]
        ]);
    }

    public function update(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if (!in_array($_SERVER['REQUEST_METHOD'], ['PUT', 'POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);
        $postId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }

        $post = $this->aiBlogPostModel->findById($postId);
        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }

        $updateData = [];
        if (isset($data['title'])) $updateData['title'] = $data['title'];
        if (isset($data['content_html'])) $updateData['content_html'] = $data['content_html'];

        if (empty($updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'No data to update'], 400);
            return;
        }

        if (!$this->aiBlogPostModel->update($postId, $updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update post'], 500);
            return;
        }
        $updatedPost = $this->aiBlogPostModel->findById($postId);
        $this->jsonResponse(['success' => true, 'message' => 'Post updated successfully', 'post' => $updatedPost]);
    }

    public function convertToRegularPost(): void
    {
        $this->jsonResponse(['success' => false, 'message' => 'Conversion to regular post not fully implemented for new AI post structure.'], 501);
        return; 
    }

    public function delete(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
         if (!in_array($_SERVER['REQUEST_METHOD'], ['DELETE', 'POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);
        $postId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }
        $post = $this->aiBlogPostModel->findById($postId);
        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }
        if (!$this->aiBlogPostModel->delete($postId)) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete post'], 500);
            return;
        }
        $this->jsonResponse(['success' => true, 'message' => 'Post deleted successfully']);
    }
}
